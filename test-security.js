#!/usr/bin/env node

/**
 * 🔐 اختبار الحماية الأمنية
 * يختبر جميع endpoints للتأكد من أنها محمية بشكل صحيح
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:8080';
const EXTERNAL_URL = 'http://***********:8080';

// ألوان للطباعة
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logTest(testName) {
  log(`\n🧪 ${testName}`, 'blue');
  log('='.repeat(50), 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * اختبار endpoint بدون مصادقة
 */
async function testUnauthorizedAccess(url, endpoint) {
  try {
    const response = await fetch(`${url}${endpoint}`);
    const data = await response.json();
    
    if (response.status === 401) {
      logSuccess(`${endpoint} محمي بشكل صحيح - يتطلب مصادقة`);
      return true;
    } else if (response.status === 200 && data.length > 0) {
      logError(`${endpoint} غير محمي! يعرض البيانات بدون مصادقة`);
      logError(`البيانات المعروضة: ${JSON.stringify(data).substring(0, 100)}...`);
      return false;
    } else {
      logWarning(`${endpoint} - استجابة غير متوقعة: ${response.status}`);
      return true;
    }
  } catch (error) {
    logError(`خطأ في اختبار ${endpoint}: ${error.message}`);
    return false;
  }
}

/**
 * اختبار endpoint بتوكن غير صحيح
 */
async function testInvalidToken(url, endpoint) {
  try {
    const response = await fetch(`${url}${endpoint}`, {
      headers: {
        'Authorization': 'Bearer invalid_token_12345',
        'X-Device-ID': 'test_device_123'
      }
    });
    
    if (response.status === 403 || response.status === 401) {
      logSuccess(`${endpoint} يرفض التوكن غير الصحيح`);
      return true;
    } else {
      logError(`${endpoint} يقبل التوكن غير الصحيح!`);
      return false;
    }
  } catch (error) {
    logError(`خطأ في اختبار التوكن لـ ${endpoint}: ${error.message}`);
    return false;
  }
}

/**
 * اختبار endpoint بدون Device ID
 */
async function testMissingDeviceId(url, endpoint) {
  try {
    const response = await fetch(`${url}${endpoint}`, {
      headers: {
        'Authorization': 'Bearer valid_token_example'
      }
    });
    
    if (response.status === 401) {
      logSuccess(`${endpoint} يتطلب Device ID`);
      return true;
    } else {
      logError(`${endpoint} لا يتطلب Device ID!`);
      return false;
    }
  } catch (error) {
    logError(`خطأ في اختبار Device ID لـ ${endpoint}: ${error.message}`);
    return false;
  }
}

/**
 * اختبار Rate Limiting
 */
async function testRateLimit(url, endpoint) {
  try {
    log(`اختبار Rate Limiting لـ ${endpoint}...`);
    
    const requests = [];
    for (let i = 0; i < 20; i++) {
      requests.push(fetch(`${url}${endpoint}`));
    }
    
    const responses = await Promise.all(requests);
    const rateLimitedResponses = responses.filter(r => r.status === 429);
    
    if (rateLimitedResponses.length > 0) {
      logSuccess(`Rate Limiting يعمل - تم رفض ${rateLimitedResponses.length} طلب`);
      return true;
    } else {
      logWarning(`Rate Limiting قد لا يعمل بشكل صحيح`);
      return true; // ليس خطأ أمني خطير
    }
  } catch (error) {
    logError(`خطأ في اختبار Rate Limiting: ${error.message}`);
    return false;
  }
}

/**
 * اختبار الوصول للملفات الحساسة
 */
async function testSensitiveFiles(url) {
  const sensitiveFiles = [
    '/.env',
    '/package.json',
    '/server.js',
    '/index.js',
    '/prisma/schema.prisma',
    '/node_modules',
    '/.git',
    '/logs'
  ];
  
  let allProtected = true;
  
  for (const file of sensitiveFiles) {
    try {
      const response = await fetch(`${url}${file}`);
      
      if (response.status === 404 || response.status === 403) {
        logSuccess(`${file} محمي بشكل صحيح`);
      } else if (response.status === 200) {
        logError(`${file} يمكن الوصول إليه!`);
        allProtected = false;
      }
    } catch (error) {
      // خطأ في الاتصال يعني أن الملف محمي
      logSuccess(`${file} محمي (خطأ اتصال)`);
    }
  }
  
  return allProtected;
}

/**
 * تشغيل جميع الاختبارات
 */
async function runAllTests() {
  log('🔐 بدء اختبار الحماية الأمنية', 'bold');
  log('=====================================', 'bold');
  
  const endpoints = [
    '/api/clients',
    '/api/agents', 
    '/api/users',
    '/api/data-records'
  ];
  
  const urls = [BASE_URL, EXTERNAL_URL];
  let totalTests = 0;
  let passedTests = 0;
  
  for (const url of urls) {
    log(`\n🌐 اختبار الخادم: ${url}`, 'yellow');
    
    // اختبار الوصول بدون مصادقة
    logTest('اختبار الوصول بدون مصادقة');
    for (const endpoint of endpoints) {
      totalTests++;
      if (await testUnauthorizedAccess(url, endpoint)) {
        passedTests++;
      }
    }
    
    // اختبار التوكن غير الصحيح
    logTest('اختبار التوكن غير الصحيح');
    for (const endpoint of endpoints) {
      totalTests++;
      if (await testInvalidToken(url, endpoint)) {
        passedTests++;
      }
    }
    
    // اختبار Device ID المفقود
    logTest('اختبار Device ID المفقود');
    for (const endpoint of endpoints) {
      totalTests++;
      if (await testMissingDeviceId(url, endpoint)) {
        passedTests++;
      }
    }
    
    // اختبار Rate Limiting
    logTest('اختبار Rate Limiting');
    totalTests++;
    if (await testRateLimit(url, '/api/clients')) {
      passedTests++;
    }
    
    // اختبار الملفات الحساسة
    logTest('اختبار حماية الملفات الحساسة');
    totalTests++;
    if (await testSensitiveFiles(url)) {
      passedTests++;
    }
  }
  
  // النتائج النهائية
  log('\n📊 نتائج الاختبار', 'bold');
  log('==================', 'bold');
  log(`إجمالي الاختبارات: ${totalTests}`);
  log(`الاختبارات الناجحة: ${passedTests}`, passedTests === totalTests ? 'green' : 'red');
  log(`الاختبارات الفاشلة: ${totalTests - passedTests}`, totalTests - passedTests === 0 ? 'green' : 'red');
  
  const successRate = ((passedTests / totalTests) * 100).toFixed(1);
  log(`معدل النجاح: ${successRate}%`, successRate === '100.0' ? 'green' : 'red');
  
  if (successRate === '100.0') {
    log('\n🎉 تهانينا! النظام محمي بالكامل', 'green');
    log('✅ جميع endpoints محمية بشكل صحيح', 'green');
    log('✅ لا يمكن الوصول للبيانات الحساسة بدون مصادقة', 'green');
  } else {
    log('\n⚠️  تحذير: هناك مشاكل أمنية تحتاج إلى إصلاح', 'red');
    log('❌ بعض endpoints غير محمية بشكل صحيح', 'red');
  }
}

// تشغيل الاختبارات
if (require.main === module) {
  runAllTests().catch(error => {
    logError(`خطأ في تشغيل الاختبارات: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  testUnauthorizedAccess,
  testInvalidToken,
  testMissingDeviceId,
  testRateLimit,
  testSensitiveFiles,
  runAllTests
};
