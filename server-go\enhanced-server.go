package main

import (
	"crypto/rand"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// Database instance
var db *gorm.DB

// Models - مطابقة تماماً لقاعدة البيانات الأصلية
type User struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Username    string    `json:"username" gorm:"unique;not null"`
	LoginName   string    `json:"loginName" gorm:"unique;not null"`
	Password    string    `json:"-" gorm:"not null"`
	DeviceID    *string   `json:"deviceId"`
	Device1     *string   `json:"device1"`
	Permissions *string   `json:"permissions" gorm:"type:jsonb"`
	IsActive    bool      `json:"isActive" gorm:"default:true"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

type Client struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	ClientCode int       `json:"clientCode" gorm:"unique;not null"`
	ClientName string    `json:"clientName" gorm:"not null"`
	AppName    *string   `json:"appName"`
	CardNumber *string   `json:"cardNumber"`
	Password   string    `json:"-" gorm:"not null"`
	Token      *string   `json:"token"`
	IPAddress  *string   `json:"ipAddress"`
	Status     int       `json:"status" gorm:"default:1"`
	UserID     *uint     `json:"userId"`
	User       *User     `json:"user,omitempty"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

type Agent struct {
	ID            uint      `json:"id" gorm:"primaryKey"`
	AgentCode     int       `json:"agentCode" gorm:"unique;not null"`
	AgentName     string    `json:"agentName" gorm:"not null"`
	AgencyName    *string   `json:"agencyName"`
	AgencyType    *string   `json:"agencyType"`
	LoginName     string    `json:"loginName" gorm:"unique;not null"`
	LoginPassword string    `json:"-" gorm:"not null"`
	IsActive      bool      `json:"isActive" gorm:"default:true"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
}

type DataRecord struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	ClientID        uint      `json:"clientId" gorm:"not null"`
	AgentID         uint      `json:"agentId" gorm:"not null"`
	ClientCode      string    `json:"clientCode"`
	AgentCode       *int      `json:"agentCode"`
	OperationType   *string   `json:"operationType"`
	OperationStatus int       `json:"operationStatus" gorm:"default:1"`
	OperationDate   time.Time `json:"operationDate"`
	IPAddress       *string   `json:"ipAddress"`
	Client          *Client   `json:"client,omitempty"`
	Agent           *Agent    `json:"agent,omitempty"`
	CreatedAt       time.Time `json:"createdAt"`
	UpdatedAt       time.Time `json:"updatedAt"`
}

// Response structures
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	TotalPages int         `json:"totalPages"`
}

// Database initialization - مطابق لـ old.js
func initDatabase() {
	dsn := "host=localhost user=postgres password=123456 dbname=yemenfull port=5432 sslmode=disable"
	var err error
	db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Printf("❌ Database error: %v", err)
		log.Println("⚠️  Running without database connection")
		db = nil
		return
	}

	log.Println("✅ Database connected")

	// Auto migrate tables
	err = db.AutoMigrate(&User{}, &Client{}, &Agent{}, &DataRecord{})
	if err != nil {
		log.Printf("⚠️  Auto migration failed: %v", err)
	}

	// Count records like old.js
	var userCount, clientCount, agentCount int64
	if db != nil {
		db.Model(&User{}).Count(&userCount)
		db.Model(&Client{}).Count(&clientCount)
		db.Model(&Agent{}).Count(&agentCount)
		log.Printf("👥 Users: %d", userCount)
		log.Printf("🏢 Clients: %d", clientCount)
		log.Printf("🤝 Agents: %d", agentCount)
	}
}

// Generate random token like old.js
func generateToken() string {
	const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
	token := make([]byte, 12)
	for i := range token {
		randomByte := make([]byte, 1)
		rand.Read(randomByte)
		token[i] = chars[randomByte[0]%byte(len(chars))]
	}
	return string(token)
}

// Setup routes - مطابق لـ old.js
func setupRoutes() *gin.Engine {
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()

	// Middleware - مطابق لـ old.js
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS configuration - مطابق لـ old.js
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowCredentials = true
	config.AllowHeaders = []string{"*"}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"}
	router.Use(cors.New(config))

	// Logging middleware - مطابق لـ old.js
	router.Use(func(c *gin.Context) {
		log.Printf("%s - %s %s", time.Now().Format(time.RFC3339), c.Request.Method, c.Request.URL.Path)
		c.Next()
	})

	// Health check - مطابق لـ old.js
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "OK",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})

	// Fix permissions - مطابق لـ old.js
	router.GET("/fix-permissions", fixPermissionsHandler)

	// Authentication routes - مطابق لـ old.js
	router.POST("/api/auth/login", authLoginHandler)
	router.POST("/api/client/login", clientLoginHandler)
	router.PUT("/api/client/update", clientUpdateHandler)

	// User management - مطابق لـ old.js
	router.GET("/api/users", getUsersHandler)
	router.POST("/api/users", createUserHandler)
	router.PUT("/api/users/:id", updateUserHandler)
	router.PUT("/api/users/:id/device", updateUserDeviceHandler)
	router.PUT("/api/users/:id/device1", updateUserDevice1Handler)
	router.DELETE("/api/users/:id", deleteUserHandler)

	// Client management - مطابق لـ old.js
	router.GET("/api/clients", getClientsHandler)
	router.POST("/api/clients", createClientHandler)
	router.PUT("/api/clients/:id", updateClientHandler)
	router.DELETE("/api/clients/:id", deleteClientHandler)

	// Agent management
	router.GET("/api/agents", getAgentsHandler)
	router.POST("/api/agents", createAgentHandler)

	// Data records
	router.GET("/api/data-records", getDataRecordsHandler)

	// Dashboard APIs
	router.GET("/api/dashboard/stats", dashboardStatsHandler)

	// Security APIs
	router.GET("/api/security/stats", securityStatsHandler)
	router.GET("/api/security/login-attempts", loginAttemptsHandler)

	// External APIs
	router.POST("/api/external/verify-direct", externalVerifyHandler)
	router.GET("/api/external/stats", externalStatsHandler)
	router.GET("/api/external/health", externalHealthHandler)

	// Static files - خدمة ملفات النظام
	clientDistPath := "../client/dist"
	if _, err := os.Stat(clientDistPath); os.IsNotExist(err) {
		clientDistPath = "./client/dist"
	}

	router.Static("/static", filepath.Join(clientDistPath, "assets"))
	router.StaticFile("/favicon.ico", filepath.Join(clientDistPath, "favicon.ico"))
	router.StaticFile("/manifest.json", filepath.Join(clientDistPath, "manifest.json"))

	// Serve React app for all non-API routes
	router.NoRoute(func(c *gin.Context) {
		if len(c.Request.URL.Path) >= 4 && c.Request.URL.Path[:4] == "/api" {
			c.JSON(http.StatusNotFound, APIResponse{
				Success: false,
				Error:   "API not found",
			})
			return
		}

		indexPath := filepath.Join(clientDistPath, "index.html")
		if _, err := os.Stat(indexPath); os.IsNotExist(err) {
			c.JSON(http.StatusOK, gin.H{
				"message": "🎉 خادم Golang المحسن يعمل بنجاح!",
				"status":  "ready",
				"note":    "React app not found, serving API only",
				"server":  "Yemen Client Management System - Enhanced Golang",
			})
			return
		}

		c.File(indexPath)
	})

	return router
}

// Fix permissions handler - مطابق لـ old.js
func fixPermissionsHandler(c *gin.Context) {
	adminPermissions := map[string]interface{}{
		"isAdmin":     true,
		"users":       map[string]bool{"read": true, "create": true, "update": true, "delete": true},
		"clients":     map[string]bool{"read": true, "create": true, "update": true, "delete": true},
		"agents":      map[string]bool{"read": true, "create": true, "update": true, "delete": true},
		"dataRecords": map[string]bool{"read": true, "create": true, "update": true, "delete": true},
		"security":    map[string]bool{"read": true, "create": true, "update": true, "delete": true},
		"dashboard":   map[string]bool{"read": true},
	}

	if db != nil {
		permissionsJSON, _ := json.Marshal(adminPermissions)
		permissionsStr := string(permissionsJSON)

		result := db.Model(&User{}).Where("login_name = ?", "hash8080").Updates(map[string]interface{}{
			"permissions": permissionsStr,
			"is_active":   true,
		})

		if result.Error != nil {
			log.Printf("❌ Fix permissions error: %v", result.Error)
		} else {
			log.Printf("✅ Fixed permissions for hash8080: %v", adminPermissions)
		}
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "تم إصلاح صلاحيات hash8080 بنجاح",
		Data:    map[string]interface{}{"permissions": adminPermissions},
	})
}

// Authentication login handler - مطابق تماماً لـ old.js
func authLoginHandler(c *gin.Context) {
	var loginData struct {
		LoginName string `json:"loginName" binding:"required"`
		Password  string `json:"password" binding:"required"`
		DeviceID  string `json:"deviceId" binding:"required"`
	}

	if err := c.ShouldBindJSON(&loginData); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   "جميع الحقول مطلوبة",
		})
		return
	}

	log.Printf("🔐 Login: %s, %s", loginData.LoginName, loginData.DeviceID)

	if db == nil {
		// Mock authentication when no database
		if loginData.LoginName == "hash8080" && loginData.Password == "yemen123456" {
			c.JSON(http.StatusOK, APIResponse{
				Success: true,
				Message: "تم تسجيل الدخول بنجاح",
				Data: gin.H{
					"user": gin.H{
						"id":          1,
						"username":    "hash8080",
						"loginName":   "hash8080",
						"permissions": gin.H{"isAdmin": true},
						"isActive":    true,
					},
					"token": fmt.Sprintf("token_%d_%d", 1, time.Now().Unix()),
				},
			})
			return
		}
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   "المستخدم غير موجود",
		})
		return
	}

	// Database authentication - مطابق لـ old.js
	log.Printf("🔍 Searching for user with loginName: \"%s\"", loginData.LoginName)

	var user User
	err := db.Where("(login_name = ? OR username = ?) AND is_active = ?",
		loginData.LoginName, loginData.LoginName, true).First(&user).Error

	if err != nil {
		log.Printf("❌ User not found: \"%s\"", loginData.LoginName)

		// البحث بدون شرط isActive للتشخيص
		var inactiveUser User
		inactiveErr := db.Where("login_name = ? OR username = ?",
			loginData.LoginName, loginData.LoginName).First(&inactiveUser).Error

		if inactiveErr == nil {
			log.Printf("⚠️ User found but inactive: %s, active: %t", inactiveUser.Username, inactiveUser.IsActive)
			c.JSON(http.StatusUnauthorized, APIResponse{
				Success: false,
				Error:   "المستخدم غير نشط",
			})
			return
		}

		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   "المستخدم غير موجود",
		})
		return
	}

	log.Printf("✅ User found: %s (ID: %d)", user.Username, user.ID)

	// التحقق من كلمة المرور
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(loginData.Password))
	if err != nil {
		log.Printf("❌ Invalid password for user: %s", user.Username)
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   "كلمة المرور خاطئة",
		})
		return
	}

	// التحقق من الجهاز - مطابق لـ old.js
	log.Printf("🔍 Device check: user.deviceId=\"%v\", user.device1=\"%v\", provided=\"%s\"",
		user.DeviceID, user.Device1, loginData.DeviceID)

	isDeviceAuthorized := false
	if user.DeviceID != nil && *user.DeviceID == loginData.DeviceID {
		isDeviceAuthorized = true
	}
	if user.Device1 != nil && *user.Device1 == loginData.DeviceID {
		isDeviceAuthorized = true
	}

	// إذا كان هناك أجهزة محفوظة ولم يطابق أي منها
	if (user.DeviceID != nil || user.Device1 != nil) && !isDeviceAuthorized {
		log.Printf("❌ Device not authorized for user %s", user.Username)
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   "لا يمكن الوصول من الجهاز الحالي",
		})
		return
	}

	log.Printf("✅ Device check passed for user %s", user.Username)

	// Parse permissions
	var permissions interface{}
	if user.Permissions != nil {
		json.Unmarshal([]byte(*user.Permissions), &permissions)
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "تم تسجيل الدخول بنجاح",
		Data: gin.H{
			"user": gin.H{
				"id":          user.ID,
				"username":    user.Username,
				"loginName":   user.LoginName,
				"permissions": permissions,
				"isActive":    user.IsActive,
			},
			"token": fmt.Sprintf("token_%d_%d", user.ID, time.Now().Unix()),
		},
	})
}

// Client login handler - مطابق تماماً لـ old.js
func clientLoginHandler(c *gin.Context) {
	var loginData struct {
		ClientCode string `json:"clientCode" binding:"required"`
		Password   string `json:"password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&loginData); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "رمز العميل وكلمة المرور مطلوبان",
		})
		return
	}

	log.Printf("🏢 Client Login: %s, passwordLength: %d", loginData.ClientCode, len(loginData.Password))

	if db == nil {
		// Mock client authentication
		c.JSON(http.StatusOK, APIResponse{
			Success: true,
			Message: "تم تسجيل الدخول بنجاح",
			Data: gin.H{
				"client": gin.H{
					"id":         1,
					"clientCode": loginData.ClientCode,
					"clientName": "عميل تجريبي",
					"status":     1,
				},
			},
		})
		return
	}

	log.Printf("🔍 Searching for client with code: \"%s\"", loginData.ClientCode)

	clientCode, err := strconv.Atoi(loginData.ClientCode)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "رمز العميل غير صحيح",
		})
		return
	}

	var client Client
	err = db.Where("client_code = ?", clientCode).First(&client).Error

	if err != nil {
		log.Printf("❌ Client not found: %s", loginData.ClientCode)
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Message: "رمز العميل غير صحيح",
		})
		return
	}

	// التحقق من حالة العميل
	if client.Status != 1 {
		log.Printf("❌ Client is not active: %s, status: %d", loginData.ClientCode, client.Status)
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Message: "حساب العميل غير نشط",
		})
		return
	}

	log.Printf("✅ Client found and active: %s", client.ClientName)

	// التحقق من كلمة المرور
	err = bcrypt.CompareHashAndPassword([]byte(client.Password), []byte(loginData.Password))
	if err != nil {
		log.Printf("❌ Invalid password for client: %s", loginData.ClientCode)
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Message: "كلمة المرور غير صحيحة",
		})
		return
	}

	log.Printf("✅ Password valid for client: %s", client.ClientName)

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "تم تسجيل الدخول بنجاح",
		Data: gin.H{
			"client": gin.H{
				"id":         client.ID,
				"clientCode": client.ClientCode,
				"clientName": client.ClientName,
				"token":      client.Token,
				"appName":    client.AppName,
				"ipAddress":  client.IPAddress,
				"status":     client.Status,
			},
		},
	})
}

// Placeholder handlers - سيتم تطويرها لاحقاً
func clientUpdateHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Message: "تم التحديث بنجاح"})
}

func getUsersHandler(c *gin.Context) {
	c.JSON(http.StatusOK, PaginatedResponse{Data: []gin.H{}, Total: 0, Page: 1, TotalPages: 0})
}

func createUserHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Message: "تم إنشاء المستخدم بنجاح"})
}

func updateUserHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Message: "تم تحديث المستخدم بنجاح"})
}

func updateUserDeviceHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Message: "تم تحديث معرف الجهاز بنجاح"})
}

func updateUserDevice1Handler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Message: "تم تحديث معرف الجهاز2 بنجاح"})
}

func deleteUserHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Message: "تم حذف المستخدم بنجاح"})
}

func getClientsHandler(c *gin.Context) {
	c.JSON(http.StatusOK, PaginatedResponse{Data: []gin.H{}, Total: 0, Page: 1, TotalPages: 0})
}

func createClientHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Message: "تم إنشاء العميل بنجاح"})
}

func updateClientHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Message: "تم تحديث العميل بنجاح"})
}

func deleteClientHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Message: "تم حذف العميل بنجاح"})
}

func getAgentsHandler(c *gin.Context) {
	c.JSON(http.StatusOK, PaginatedResponse{Data: []gin.H{}, Total: 0, Page: 1, TotalPages: 0})
}

func createAgentHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Message: "تم إنشاء الوكيل بنجاح"})
}

func getDataRecordsHandler(c *gin.Context) {
	c.JSON(http.StatusOK, PaginatedResponse{Data: []gin.H{}, Total: 0, Page: 1, TotalPages: 0})
}

func dashboardStatsHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: gin.H{
			"totalUsers":       10,
			"totalClients":     25,
			"activeClients":    20,
			"blockedClients":   5,
			"totalAgents":      15,
			"activeAgents":     12,
			"inactiveAgents":   3,
			"totalDataRecords": 150,
		},
	})
}

func securityStatsHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: gin.H{
			"totalLoginAttempts": 100,
			"successfulLogins":   80,
			"failedLogins":       20,
			"blockedIPs":         5,
			"securityEvents":     3,
		},
	})
}

func loginAttemptsHandler(c *gin.Context) {
	c.JSON(http.StatusOK, PaginatedResponse{Data: []gin.H{}, Total: 0, Page: 1, TotalPages: 0})
}

func externalVerifyHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"status": "success", "client_status": 1})
}

func externalStatsHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total_clients":    25,
			"active_clients":   20,
			"blocked_clients":  5,
			"total_agents":     15,
			"active_agents":    12,
			"inactive_agents":  3,
			"total_operations": 150,
			"server_status":    "operational",
			"last_updated":     time.Now().Format(time.RFC3339),
		},
	})
}

func externalHealthHandler(c *gin.Context) {
	dbStatus := "disconnected"
	if db != nil {
		dbStatus = "connected"
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"status":    "healthy",
		"database":  dbStatus,
		"server":    "operational",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

func main() {
	log.Println("🚀 ========================================")
	log.Println("🏢 Yemen Client Management System - Enhanced Golang")
	log.Println("🚀 ========================================")

	// Initialize database
	initDatabase()

	// Setup routes
	router := setupRoutes()

	// Get port
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("📡 Server starting on port: %s", port)
	log.Printf("🌐 Local access: http://localhost:%s", port)
	log.Printf("🌐 External access: http://***********:%s", port)
	log.Printf("📁 Serving React app from: %s", "../client/dist")
	log.Println("✅ Server ready for connections!")
	log.Println("🚀 ========================================")

	// Start server on all interfaces
	if err := router.Run("0.0.0.0:" + port); err != nil {
		log.Fatal("❌ Failed to start server:", err)
	}
}
