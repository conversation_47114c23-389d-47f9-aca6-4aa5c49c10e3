@echo off
chcp 65001 >nul
echo ========================================
echo       🧹 تنظيف النظام من الملفات غير الضرورية
echo ========================================
echo.

echo 📋 فحص الملفات المؤقتة والغير ضرورية...
echo.

REM حذف ملفات logs القديمة (أكثر من 7 أيام)
if exist "server\logs" (
    echo 🗂️  تنظيف ملفات اللوقات القديمة...
    forfiles /p "server\logs" /s /m *.log /d -7 /c "cmd /c del @path" 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم حذف ملفات اللوقات القديمة
    ) else (
        echo ℹ️  لا توجد ملفات لوقات قديمة للحذف
    )
)

REM حذف ملفات node_modules المؤقتة
echo.
echo 🗂️  فحص ملفات node_modules...
if exist "node_modules" (
    echo ⚠️  تم العثور على node_modules في المجلد الجذر
    echo هذا المجلد غير ضروري، هل تريد حذفه؟ (y/n)
    set /p choice="اختر: "
    if /i "%choice%"=="y" (
        echo حذف node_modules...
        rmdir /s /q "node_modules" 2>nul
        echo ✅ تم حذف node_modules من المجلد الجذر
    )
)

REM حذف ملفات package-lock.json المكررة
echo.
echo 🗂️  فحص ملفات package-lock.json المكررة...
if exist "package-lock.json" (
    echo ⚠️  تم العثور على package-lock.json في المجلد الجذر
    echo هذا الملف غير ضروري، هل تريد حذفه؟ (y/n)
    set /p choice="اختر: "
    if /i "%choice%"=="y" (
        del "package-lock.json" 2>nul
        echo ✅ تم حذف package-lock.json من المجلد الجذر
    )
)

REM حذف ملفات .env المكررة أو الاختبارية
echo.
echo 🗂️  فحص ملفات .env...
if exist ".env.example" (
    echo ℹ️  .env.example موجود (هذا طبيعي)
)
if exist ".env.test" (
    echo ⚠️  تم العثور على .env.test
    echo هل تريد حذفه؟ (y/n)
    set /p choice="اختر: "
    if /i "%choice%"=="y" (
        del ".env.test" 2>nul
        echo ✅ تم حذف .env.test
    )
)

REM حذف ملفات النسخ الاحتياطية
echo.
echo 🗂️  فحص ملفات النسخ الاحتياطية...
for %%f in (*.bak *.backup *.old *.tmp) do (
    if exist "%%f" (
        echo ⚠️  تم العثور على ملف نسخة احتياطية: %%f
        echo هل تريد حذفه؟ (y/n)
        set /p choice="اختر: "
        if /i "!choice!"=="y" (
            del "%%f" 2>nul
            echo ✅ تم حذف %%f
        )
    )
)

REM تنظيف مجلد client/dist القديم
echo.
echo 🗂️  فحص مجلد client/dist...
if exist "client\dist" (
    echo ℹ️  مجلد client/dist موجود
    echo هل تريد إعادة بناء الواجهة الأمامية؟ (y/n)
    set /p choice="اختر: "
    if /i "%choice%"=="y" (
        echo 🔨 إعادة بناء الواجهة الأمامية...
        cd client
        call npm run build
        cd ..
        echo ✅ تم إعادة بناء الواجهة الأمامية
    )
)

REM فحص الملفات المكررة
echo.
echo 🗂️  فحص الملفات المكررة...

REM فحص ملفات package.json المكررة
if exist "package-new.json" (
    echo ⚠️  تم العثور على package-new.json
    echo هذا الملف قد يكون مكرر، هل تريد حذفه؟ (y/n)
    set /p choice="اختر: "
    if /i "%choice%"=="y" (
        del "package-new.json" 2>nul
        echo ✅ تم حذف package-new.json
    )
)

REM تنظيف ملفات الاختبار المؤقتة
echo.
echo 🗂️  تنظيف ملفات الاختبار المؤقتة...
for %%f in (test-*.tmp test-*.log debug-*.txt) do (
    if exist "%%f" (
        del "%%f" 2>nul
        echo ✅ تم حذف %%f
    )
)

REM عرض حجم المجلدات الرئيسية
echo.
echo 📊 حجم المجلدات الرئيسية:
echo ============================
if exist "server\node_modules" (
    for /f "tokens=3" %%a in ('dir "server\node_modules" /-c ^| find "File(s)"') do echo 📁 server/node_modules: %%a bytes
)
if exist "client\node_modules" (
    for /f "tokens=3" %%a in ('dir "client\node_modules" /-c ^| find "File(s)"') do echo 📁 client/node_modules: %%a bytes
)
if exist "client\dist" (
    for /f "tokens=3" %%a in ('dir "client\dist" /-c ^| find "File(s)"') do echo 📁 client/dist: %%a bytes
)

echo.
echo ✅ تم الانتهاء من تنظيف النظام
echo.
echo 💡 نصائح للحفاظ على النظام نظيفاً:
echo   - قم بتشغيل هذا السكريبت أسبوعياً
echo   - احذف ملفات اللوقات القديمة بانتظام
echo   - تأكد من عدم وجود ملفات مكررة
echo   - استخدم .gitignore لتجنب رفع الملفات غير الضرورية
echo.
echo ========================================
echo        انتهى تنظيف النظام
echo ========================================
pause
