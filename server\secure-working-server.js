const express = require('express');
const cors = require('cors');
const path = require('path');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

// إعداد Prisma
const prisma = new PrismaClient();

// إعداد Express
const app = express();
const PORT = 8080;

console.log('🔐 Starting Secure Working Server...');

// إعدادات الحماية الأساسية
const helmetConfig = helmet({
  hidePoweredBy: true,
  frameguard: { action: 'deny' },
  noSniff: true,
  xssFilter: true,
  referrerPolicy: { policy: 'no-referrer' }
});

// Rate Limiting
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 100, // 100 طلب كحد أقصى
  message: { error: 'تم تجاوز الحد المسموح من الطلبات لاتحاول' },
  standardHeaders: false,
  legacyHeaders: false
});

const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 5, // 5 محاولات فقط
  message: { error: 'تم تجاوز محاولات تسجيل الدخول المسموحة لاتحاول' },
  standardHeaders: false,
  legacyHeaders: false
});

const apiLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 دقائق
  max: 20, // 20 طلب كحد أقصى للـ APIs
  message: { error: 'تم تجاوز الحد المسموح للعمليات الحساسة لاتحاول' },
  standardHeaders: false,
  legacyHeaders: false
});

// Middleware للحماية
app.use(helmetConfig);
app.use(generalLimiter);

// إخفاء معلومات الخادم
app.use((req, res, next) => {
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');
  res.setHeader('Server', 'Apache/2.4.41');
  res.setHeader('X-Powered-By', 'PHP/7.4.3');
  next();
});

// منع الوصول للملفات الحساسة
app.use((req, res, next) => {
  const blockedPaths = [
    '/.env', '/package.json', '/package-lock.json', '/node_modules',
    '/.git', '/prisma', '/logs', '/config', '/.vscode', '/src'
  ];

  if (blockedPaths.some(blocked => req.path.toLowerCase().includes(blocked))) {
    console.log(`🚨 محاولة وصول لملف حساس: ${req.path} من IP: ${req.ip}`);
    return res.status(404).json({ error: 'الصفحة غير موجودة لاتحاول' });
  }
  next();
});

// منع SQL Injection
app.use((req, res, next) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /(\'|\"|;|--|\*|\|)/,
    /(\bUNION\b.*\bSELECT\b)/i
  ];

  const checkForSQLInjection = (obj) => {
    for (let key in obj) {
      if (typeof obj[key] === 'string') {
        for (let pattern of sqlPatterns) {
          if (pattern.test(obj[key])) {
            console.log(`🚨 محاولة SQL Injection من IP: ${req.ip}`);
            return true;
          }
        }
      }
    }
    return false;
  };

  if (checkForSQLInjection(req.query) || checkForSQLInjection(req.body)) {
    return res.status(400).json({ error: 'بيانات غير صحيحة لاتحاول' });
  }
  next();
});

// تتبع النشاط المشبوه
app.use((req, res, next) => {
  const suspiciousAgents = [/sqlmap/i, /nikto/i, /nmap/i, /masscan/i, /zap/i, /burp/i];
  const userAgent = req.get('User-Agent') || '';

  if (suspiciousAgents.some(pattern => pattern.test(userAgent))) {
    console.log(`🚨 User Agent مشبوه: ${userAgent} من IP: ${req.ip}`);
    return res.status(403).json({ error: 'وصول مرفوض لاتحاول' });
  }
  next();
});

// CORS مع إعدادات آمنة
app.use(cors({
  origin: ['http://localhost:3000', 'http://***********:8080', 'http://**************:8080'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging آمن
app.use((req, res, next) => {
  const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress;
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path} from ${clientIP}`);
  next();
});

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Middleware للتحقق من التوكن (للـ APIs الحساسة فقط)
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  const deviceId = req.headers['x-device-id'];

  // السماح ببعض المسارات بدون توكن
  const publicPaths = ['/health', '/api/test', '/api/client/login', '/api/agent/login'];
  if (publicPaths.includes(req.path)) {
    return next();
  }

  if (!token) {
    return res.status(401).json({ error: 'Access token required لاتحاول' });
  }

  if (!deviceId) {
    return res.status(401).json({ error: 'Device ID required لاتحاول' });
  }

  jwt.verify(token, JWT_SECRET, async (err, decoded) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token لاتحاول' });
    }

    try {
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: { id: true, username: true, loginName: true, deviceId: true, isActive: true }
      });

      if (!user || !user.isActive) {
        return res.status(401).json({ error: 'User not found or inactive لاتحاول' });
      }

      // التحقق من Device ID
      if (user.deviceId && user.deviceId !== deviceId) {
        return res.status(403).json({ error: 'Device not authorized لاتحاول' });
      }

      req.user = user;
      req.deviceId = deviceId;
      next();
    } catch (error) {
      return res.status(500).json({ error: 'Authentication error لاتحاول' });
    }
  });
};

// Test database connection
prisma.$connect().then(() => {
  console.log('✅ Database connected');
  prisma.user.count().then(count => console.log(`👥 Users: ${count}`));
  prisma.client.count().then(count => console.log(`🏢 Clients: ${count}`));
  prisma.agent.count().then(count => console.log(`🤝 Agents: ${count}`));
}).catch(err => console.error('❌ Database error:', err));

// Health check
app.get('/health', async (req, res) => {
  try {
    await prisma.$connect();
    const userCount = await prisma.user.count();
    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      database: 'connected',
      userCount: userCount
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      database: 'disconnected'
    });
  }
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'API يعمل بشكل صحيح',
    timestamp: new Date().toISOString(),
    server: 'Secure Working Server',
    database: 'connected'
  });
});

// User login API
app.post('/api/user/login', loginLimiter, async (req, res) => {
  try {
    const { loginName, password, deviceId } = req.body;
    console.log('🔐 User Login attempt:', { loginName, deviceId });

    if (!loginName || !password || !deviceId) {
      return res.status(400).json({
        success: false,
        message: 'جميع الحقول مطلوبة'
      });
    }

    const user = await prisma.user.findUnique({
      where: { loginName },
      select: {
        id: true,
        username: true,
        loginName: true,
        password: true,
        deviceId: true,
        isActive: true,
        permissions: true
      }
    });

    if (!user) {
      console.log('❌ User not found:', loginName);
      return res.status(401).json({
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    if (!user.isActive) {
      console.log('❌ User inactive:', loginName);
      return res.status(401).json({
        success: false,
        message: 'الحساب غير نشط'
      });
    }

    const passwordValid = await bcrypt.compare(password, user.password);
    if (!passwordValid) {
      console.log('❌ Invalid password for user:', loginName);
      return res.status(401).json({
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    // التحقق من Device ID أو تحديثه
    if (!user.deviceId) {
      await prisma.user.update({
        where: { id: user.id },
        data: { deviceId: deviceId }
      });
    } else if (user.deviceId !== deviceId) {
      console.log('⚠️ Device ID mismatch for user:', loginName);
      return res.status(403).json({
        success: false,
        message: 'هذا الحساب مرتبط بجهاز آخر'
      });
    }

    // إنشاء JWT Token
    const token = jwt.sign(
      { userId: user.id, loginName: user.loginName },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    console.log('✅ User login successful:', loginName);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions
      },
      token: token
    });

  } catch (error) {
    console.error('User login error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

// Client login API (بدون حماية مشددة للعملاء)
app.post('/api/client/login', loginLimiter, async (req, res) => {
  try {
    const { clientCode, password } = req.body;
    console.log('🏢 Client Login attempt:', { clientCode });

    if (!clientCode || !password) {
      return res.status(400).json({
        success: false,
        message: 'رمز العميل وكلمة المرور مطلوبان'
      });
    }

    const client = await prisma.client.findUnique({
      where: { clientCode: parseInt(clientCode) }
    });

    if (!client) {
      console.log('❌ Client not found:', clientCode);
      return res.status(401).json({
        success: false,
        message: 'رمز العميل أو كلمة المرور غير صحيحة'
      });
    }

    if (client.status !== 1) {
      console.log('❌ Client inactive:', clientCode);
      return res.status(401).json({
        success: false,
        message: 'حساب العميل غير نشط'
      });
    }

    const passwordValid = await bcrypt.compare(password, client.password);
    if (!passwordValid) {
      console.log('❌ Invalid password for client:', clientCode);
      return res.status(401).json({
        success: false,
        message: 'رمز العميل أو كلمة المرور غير صحيحة'
      });
    }

    console.log('✅ Client login successful:', clientCode);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      client: {
        id: client.id,
        clientCode: client.clientCode,
        clientName: client.clientName,
        token: client.token,
        appName: client.appName,
        ipAddress: client.ipAddress,
        status: client.status
      }
    });

  } catch (error) {
    console.error('Client login error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

// Users API - محمي
app.get('/api/users', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (page - 1) * limit;

    const where = search ? {
      OR: [
        { username: { contains: search, mode: 'insensitive' } },
        { loginName: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        select: {
          id: true,
          username: true,
          loginName: true,
          deviceId: true,
          permissions: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          _count: { select: { clients: true } }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    console.log(`✅ Users API: Retrieved ${users.length} users for user ${req.user.id}`);
    res.json({
      data: users,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Users error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// Clients API - محمي
app.get('/api/clients', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, userId, search = '' } = req.query;
    const skip = (page - 1) * limit;

    let where = {};
    if (userId) {
      where.userId = parseInt(userId);
    }
    if (search) {
      where.OR = [
        { clientName: { contains: search, mode: 'insensitive' } },
        { appName: { contains: search, mode: 'insensitive' } }
      ];
    }

    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          user: { select: { id: true, username: true, loginName: true } }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.client.count({ where })
    ]);

    console.log(`✅ Clients API: Retrieved ${clients.length} clients for user ${req.user.id}`);
    res.json({
      data: clients,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Clients error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// Agents API - محمي
app.get('/api/agents', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (page - 1) * limit;

    const where = search ? {
      OR: [
        { agentName: { contains: search, mode: 'insensitive' } },
        { agencyName: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        select: {
          id: true,
          agentName: true,
          agencyName: true,
          agencyType: true,
          ipAddress: true,
          loginName: true,
          isActive: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.agent.count({ where })
    ]);

    console.log(`✅ Agents API: Retrieved ${agents.length} agents for user ${req.user.id}`);
    res.json({
      data: agents,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Agents error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// Data Records API - محمي
app.get('/api/data-records', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, agentId, clientId } = req.query;
    const skip = (page - 1) * limit;

    let where = {};
    if (agentId) where.agentId = parseInt(agentId);
    if (clientId) where.clientId = parseInt(clientId);

    const [dataRecords, total] = await Promise.all([
      prisma.dataRecord.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          agent: { select: { agentName: true } },
          client: { select: { clientName: true } }
        },
        orderBy: { operationDate: 'desc' }
      }),
      prisma.dataRecord.count({ where })
    ]);

    console.log(`✅ Data Records API: Retrieved ${dataRecords.length} records for user ${req.user.id}`);
    res.json({
      data: dataRecords,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Data records error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// Static files
app.use(express.static(path.join(__dirname, '../client/dist')));

// React app fallback
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API not found لاتحاول' });
  }
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// Error handling
app.use((err, req, res, next) => {
  console.error('خطأ في التطبيق:', err);
  res.status(500).json({
    error: 'حدث خطأ في الخادم لاتحاول',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Secure Working Server running on http://0.0.0.0:${PORT}`);
  console.log(`🌐 External: http://***********:${PORT}`);
  console.log(`🏠 Local: http://localhost:${PORT}`);
  console.log(`🔐 Security: ENABLED`);
  console.log(`🛡️ Rate Limiting: ACTIVE`);
  console.log(`🚨 Threat Protection: ACTIVE`);
});

console.log('🎯 Secure Working Server initialized with protection!');
