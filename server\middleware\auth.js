const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Middleware للتحقق من صحة JWT ورقم الجهاز
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  const deviceId = req.headers['x-device-id']; // رقم الجهاز من الهيدر

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  if (!deviceId) {
    return res.status(401).json({ error: 'Device ID required' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // التحقق من وجود المستخدم في قاعدة البيانات
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        permissions: true,
        isActive: true
      }
    });

    if (!user || !user.isActive) {
      return res.status(401).json({ error: 'User not found or inactive' });
    }

    // التحقق من رقم الجهاز (يدعم أجهزة متعددة)
    if (user.deviceId) {
      const allowedDevices = user.deviceId.includes(',')
        ? user.deviceId.split(',').map(id => id.trim())
        : [user.deviceId];

      if (!allowedDevices.includes(deviceId)) {
        return res.status(403).json({
          error: 'Device not authorized',
          message: 'هذا الحساب مرتبط بأجهزة أخرى. يرجى تسجيل الدخول من أحد الأجهزة المصرح بها.',
          authorizedDevices: allowedDevices,
          currentDevice: deviceId
        });
      }
    }

    req.user = user;
    req.deviceId = deviceId;

    next();
  } catch (error) {
    console.error('Token verification error:', error);
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
};

// Middleware للتحقق من الصلاحيات
const checkPermission = (resource, action) => {
  return (req, res, next) => {
    const userPermissions = req.user.permissions;

    // التحقق من صلاحية الأدمن
    if (userPermissions.isAdmin) {
      return next();
    }

    // التحقق من الصلاحية المحددة
    if (userPermissions[resource] && userPermissions[resource][action]) {
      return next();
    }

    return res.status(403).json({
      error: 'Insufficient permissions',
      required: `${resource}.${action}`
    });
  };
};

// Middleware لتسجيل محاولات الدخول
const logLoginAttempt = async (userType, userId, agentId, deviceId, ipAddress, success) => {
  try {
    await prisma.loginAttempt.create({
      data: {
        userType,
        userId,
        agentId,
        deviceId,
        ipAddress,
        success
      }
    });
  } catch (error) {
    console.error('Error logging login attempt:', error);
  }
};

module.exports = {
  authenticateToken,
  checkPermission,
  logLoginAttempt
};
