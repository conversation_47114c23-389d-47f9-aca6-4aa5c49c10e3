const TokenManager = require('../utils/token-manager')
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

/**
 * Middleware للتحقق من مصادقة الوكيل
 */
const authenticateAgent = async (req, res, next) => {
  try {
    // استخراج التوكن من header
    const authHeader = req.headers.authorization

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        status: 'error',
        message: 'Missing or invalid authorization header لاتحاول',
        error_code: 'MISSING_TOKEN'
      })
    }

    const token = authHeader.substring(7) // إزالة "Bearer "

    // التحقق من صحة التوكن
    const agentData = await TokenManager.verifyAgentToken(token)

    if (!agentData) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid or expired agent token لاتحاول',
        error_code: 'INVALID_AGENT_TOKEN'
      })
    }

    // التحقق من أن الوكيل نشط
    if (!agentData.agent.isActive) {
      return res.status(403).json({
        status: 'error',
        message: 'Agent account is inactive لاتحاول',
        error_code: 'AGENT_INACTIVE'
      })
    }

    // إضافة بيانات الوكيل إلى الطلب
    req.agent = agentData.agent
    req.agentSession = {
      sessionId: agentData.sessionId,
      token: token
    }

    next()
  } catch (error) {
    console.error('Agent authentication error:', error)
    res.status(500).json({
      status: 'error',
      message: 'Authentication service error',
      error_code: 'AUTH_SERVICE_ERROR'
    })
  }
}

/**
 * Middleware لتسجيل محاولات الوصول للAPI
 */
const logApiAccess = async (req, res, next) => {
  try {
    const startTime = Date.now()

    // تسجيل معلومات الطلب
    const requestLog = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      timestamp: new Date(),
      agentId: req.agent ? req.agent.id : null
    }

    console.log('API Access:', requestLog)

    // إضافة معلومات الطلب إلى req
    req.requestLog = requestLog

    // تسجيل وقت الاستجابة
    res.on('finish', () => {
      const duration = Date.now() - startTime
      console.log(`API Response: ${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms`)
    })

    next()
  } catch (error) {
    console.error('API logging error:', error)
    next() // لا نوقف الطلب بسبب خطأ في التسجيل
  }
}

/**
 * Middleware للتحقق من معدل الطلبات (Rate Limiting)
 */
const rateLimiter = (() => {
  const requests = new Map() // تخزين مؤقت للطلبات
  const WINDOW_SIZE = 60 * 1000 // نافذة زمنية: دقيقة واحدة
  const MAX_REQUESTS_PER_MINUTE = 100 // حد أقصى 100 طلب/دقيقة
  const MAX_REQUESTS_PER_HOUR = 1000 // حد أقصى 1000 طلب/ساعة

  return async (req, res, next) => {
    try {
      const agentId = req.agent ? req.agent.id : req.ip
      const now = Date.now()

      // تنظيف الطلبات القديمة
      if (!requests.has(agentId)) {
        requests.set(agentId, {
          minute: [],
          hour: []
        })
      }

      const agentRequests = requests.get(agentId)

      // تنظيف الطلبات الأقدم من دقيقة
      agentRequests.minute = agentRequests.minute.filter(
        timestamp => now - timestamp < WINDOW_SIZE
      )

      // تنظيف الطلبات الأقدم من ساعة
      agentRequests.hour = agentRequests.hour.filter(
        timestamp => now - timestamp < 60 * WINDOW_SIZE
      )

      // فحص حد الطلبات
      if (agentRequests.minute.length >= MAX_REQUESTS_PER_MINUTE) {
        return res.status(429).json({
          status: 'error',
          message: 'Too many requests per minute',
          error_code: 'RATE_LIMIT_EXCEEDED',
          retry_after: 60
        })
      }

      if (agentRequests.hour.length >= MAX_REQUESTS_PER_HOUR) {
        return res.status(429).json({
          status: 'error',
          message: 'Too many requests per hour',
          error_code: 'RATE_LIMIT_EXCEEDED',
          retry_after: 3600
        })
      }

      // إضافة الطلب الحالي
      agentRequests.minute.push(now)
      agentRequests.hour.push(now)

      // إضافة headers للحدود
      res.set({
        'X-RateLimit-Limit-Minute': MAX_REQUESTS_PER_MINUTE,
        'X-RateLimit-Remaining-Minute': MAX_REQUESTS_PER_MINUTE - agentRequests.minute.length,
        'X-RateLimit-Limit-Hour': MAX_REQUESTS_PER_HOUR,
        'X-RateLimit-Remaining-Hour': MAX_REQUESTS_PER_HOUR - agentRequests.hour.length,
        'X-RateLimit-Reset': Math.ceil((now + WINDOW_SIZE) / 1000)
      })

      next()
    } catch (error) {
      console.error('Rate limiting error:', error)
      next() // لا نوقف الطلب بسبب خطأ في Rate Limiting
    }
  }
})()

/**
 * Middleware لإضافة headers الأمان
 */
const securityHeaders = (req, res, next) => {
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'",
    'X-API-Version': '1.0',
    'X-Powered-By': 'Yemen Client Management System'
  })
  next()
}

/**
 * Middleware للتعامل مع الأخطاء
 */
const errorHandler = (error, req, res, next) => {
  console.error('API Error:', error)

  // تسجيل الخطأ في قاعدة البيانات (اختياري)
  if (req.agent) {
    // يمكن إضافة تسجيل الأخطاء هنا
  }

  // إرجاع رد خطأ موحد
  res.status(500).json({
    status: 'error',
    message: 'Internal server error',
    error_code: 'SERVER_ERROR',
    timestamp: new Date().toISOString()
  })
}

module.exports = {
  authenticateAgent,
  logApiAccess,
  rateLimiter,
  securityHeaders,
  errorHandler
}
