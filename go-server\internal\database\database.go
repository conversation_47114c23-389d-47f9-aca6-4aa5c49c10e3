package database

import (
	"fmt"
	"log"
	"os"
	"time"

	"yemclient-go/internal/models"
	
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// Config إعدادات قاعدة البيانات
type Config struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
}

// Connect الاتصال بقاعدة البيانات
func Connect() error {
	config := Config{
		Host:     getEnv("DB_HOST", "localhost"),
		Port:     getEnv("DB_PORT", "5432"),
		User:     getEnv("DB_USER", "postgres"),
		Password: getEnv("DB_PASSWORD", "yemen123"),
		DBName:   getEnv("DB_NAME", "yemclient_db"),
		SSLMode:  getEnv("DB_SSLMODE", "disable"),
	}

	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		config.Host, config.Port, config.User, config.Password, config.DBName, config.SSLMode,
	)

	// إعداد logger
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	var err error
	DB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: newLogger,
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})

	if err != nil {
		return fmt.Errorf("فشل في الاتصال بقاعدة البيانات: %v", err)
	}

	// إعداد connection pool
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("فشل في الحصول على SQL DB: %v", err)
	}

	// إعدادات الأداء
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	log.Println("✅ تم الاتصال بقاعدة البيانات بنجاح")
	return nil
}

// Migrate تشغيل migrations
func Migrate() error {
	log.Println("🔄 تشغيل database migrations...")

	err := DB.AutoMigrate(
		&models.User{},
		&models.Client{},
		&models.Agent{},
		&models.DataRecord{},
		&models.LoginAttempt{},
		&models.SecurityLog{},
	)

	if err != nil {
		return fmt.Errorf("فشل في تشغيل migrations: %v", err)
	}

	log.Println("✅ تم تشغيل migrations بنجاح")
	return nil
}

// CreateIndexes إنشاء الفهارس
func CreateIndexes() error {
	log.Println("🔄 إنشاء database indexes...")

	// فهارس الأداء
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_users_login_name ON users(login_name)",
		"CREATE INDEX IF NOT EXISTS idx_users_device_id ON users(device_id)",
		"CREATE INDEX IF NOT EXISTS idx_clients_client_code ON clients(client_code)",
		"CREATE INDEX IF NOT EXISTS idx_clients_user_id ON clients(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_agents_login_name ON agents(login_name)",
		"CREATE INDEX IF NOT EXISTS idx_data_records_agent_id ON data_records(agent_id)",
		"CREATE INDEX IF NOT EXISTS idx_data_records_client_id ON data_records(client_id)",
		"CREATE INDEX IF NOT EXISTS idx_data_records_operation_date ON data_records(operation_date)",
		"CREATE INDEX IF NOT EXISTS idx_login_attempts_ip_address ON login_attempts(ip_address)",
		"CREATE INDEX IF NOT EXISTS idx_login_attempts_created_at ON login_attempts(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON security_logs(event_type)",
		"CREATE INDEX IF NOT EXISTS idx_security_logs_ip_address ON security_logs(ip_address)",
		"CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs(created_at)",
	}

	for _, index := range indexes {
		if err := DB.Exec(index).Error; err != nil {
			log.Printf("⚠️ فشل في إنشاء فهرس: %v", err)
		}
	}

	log.Println("✅ تم إنشاء indexes بنجاح")
	return nil
}

// SeedData إدراج البيانات الأولية
func SeedData() error {
	log.Println("🌱 إدراج البيانات الأولية...")

	// التحقق من وجود مستخدم admin
	var adminUser models.User
	result := DB.Where("login_name = ?", "hash8080").First(&adminUser)
	
	if result.Error == gorm.ErrRecordNotFound {
		// إنشاء مستخدم admin
		hashedPassword, err := HashPassword("yemen123456")
		if err != nil {
			return fmt.Errorf("فشل في تشفير كلمة المرور: %v", err)
		}

		permissions := `{
			"isAdmin": true,
			"users": {"read": true, "create": true, "update": true, "delete": true},
			"clients": {"read": true, "create": true, "update": true, "delete": true},
			"agents": {"read": true, "create": true, "update": true, "delete": true},
			"dataRecords": {"read": true, "create": true, "update": true, "delete": true}
		}`

		adminUser = models.User{
			Username:    "محمد الحاشدي",
			LoginName:   "hash8080",
			Password:    hashedPassword,
			Permissions: &permissions,
			IsActive:    true,
		}

		if err := DB.Create(&adminUser).Error; err != nil {
			return fmt.Errorf("فشل في إنشاء مستخدم admin: %v", err)
		}

		log.Println("✅ تم إنشاء مستخدم admin: hash8080")
	}

	log.Println("✅ تم إدراج البيانات الأولية بنجاح")
	return nil
}

// Close إغلاق الاتصال
func Close() error {
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// GetDB الحصول على instance قاعدة البيانات
func GetDB() *gorm.DB {
	return DB
}

// getEnv الحصول على متغير البيئة مع قيمة افتراضية
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// HashPassword تشفير كلمة المرور
func HashPassword(password string) (string, error) {
	// سيتم تنفيذها في ملف auth منفصل
	return password, nil // مؤقت
}
