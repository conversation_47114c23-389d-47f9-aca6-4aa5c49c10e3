@echo off
echo 🚀 تشغيل خادم النظام...
echo ========================

cd /d C:\yemclinet

echo 📋 التحقق من المجلد الحالي...
echo %CD%

echo 📦 التحقق من ملفات الخادم...
if exist "server\index.js" (
    echo ✅ تم العثور على index.js
) else (
    echo ❌ لم يتم العثور على index.js
)

echo.
echo 🔧 محاولة تشغيل الخادم...
echo ========================

echo 📝 تشغيل الخادم الرئيسي: index.js
node server\index.js

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تشغيل الخادم الرئيسي
    echo 💡 تحقق من تثبيت Node.js والمكتبات المطلوبة
    echo.
    echo 🔧 لحل المشكلة:
    echo 1. تأكد من تثبيت Node.js
    echo 2. شغل: npm install في مجلد server
    echo 3. تحقق من ملف index.js
    echo 4. تحقق من قاعدة البيانات PostgreSQL
)

echo.
echo ⏸️ اضغط أي مفتاح للخروج...
pause > nul
