﻿/**
 * خادم API بسيط للوكلاء الخارجيين
 * Simple API server for external agents
 */

const express = require('express');
const cors = require('cors');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const path = require('path');
const crypto = require('crypto');
const app = express();
const prisma = new PrismaClient();
const PORT = process.env.PORT || 8081;





function md5Hash(data) {
  return crypto.createHash('md5').update(data).digest('hex');
 }
 

 
// Middleware
app.use(cors({
  origin: '*',
  credentials: true
}));

app.use(express.json());

// Logging function
function writeLog(message) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
}

// Health Check
app.get('/health', async (req, res) => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    res.json({
      status: 'OK',
      database: 'connected',
      timestamp: new Date().toISOString(),
      server: 'API Server'
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      database: 'disconnected',
      error: error.message
    });
  }
});

// Auth API: Login
app.post('/api/auth/login', async (req, res) => {
  try {
    const { loginName, password, deviceId } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';

    writeLog(`🔐 Login attempt: ${loginName} from device ${deviceId?.substring(0, 10)}... IP: ${clientIP}`);

    // التحقق من وجود deviceId
    if (!deviceId) {
      writeLog(`❌ Device ID missing for: ${loginName}`);
      return res.status(400).json({
        success: false,
        message: 'Device ID مطلوب للدخول'
      });
    }

    // البحث عن المستخدم
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: loginName },
          { loginName: loginName }
        ],
        isActive: true
      }
    });

    if (!user) {
      writeLog(`❌ User not found: ${loginName}`);
      return res.status(401).json({
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      writeLog(`❌ Invalid password for: ${loginName}`);
      return res.status(401).json({
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    // التحقق من الجهاز - Device Validation صارم
    let isDeviceAuthorized = false;
    let authorizedDevices = [];

    // فحص device1 أولاً (العمود الجديد)
    if (user.device1) {
      authorizedDevices.push(user.device1);
      if (user.device1 === deviceId) {
        isDeviceAuthorized = true;
        writeLog(`✅ Device authorized via device1: ${deviceId.substring(0, 10)}...`);
      }
    }

    // فحص deviceId (العمود القديم) إذا لم يتم العثور على الجهاز في device1
    if (!isDeviceAuthorized && user.deviceId) {
      const oldDevices = user.deviceId.includes(',')
        ? user.deviceId.split(',').map(id => id.trim())
        : [user.deviceId];

      authorizedDevices = [...authorizedDevices, ...oldDevices];

      if (oldDevices.includes(deviceId)) {
        isDeviceAuthorized = true;
        writeLog(`✅ Device authorized via deviceId: ${deviceId.substring(0, 10)}...`);
      }
    }

    // إذا لم يكن هناك أجهزة مسجلة، السماح بالدخول وتسجيل الجهاز
    if (!user.device1 && !user.deviceId) {
      writeLog('📱 No devices registered, registering current device');

      // تحديث device1 بالجهاز الحالي
      await prisma.user.update({
        where: { id: user.id },
        data: { device1: deviceId }
      });

      isDeviceAuthorized = true;
      authorizedDevices = [deviceId];
    }

    // رفض الدخول إذا لم يكن الجهاز مصرحاً
    if (!isDeviceAuthorized) {
      writeLog(`❌ Device not authorized: ${deviceId.substring(0, 10)}... for user: ${loginName}`);
      writeLog(`   Authorized devices: ${authorizedDevices.join(', ')}`);

      return res.status(403).json({
        success: false,
        message: 'هذا الجهاز غير مصرح له بالدخول',
        error: 'يرجى تسجيل الدخول من أحد الأجهزة المصرح بها',
        authorizedDevices: authorizedDevices.map(id => id.substring(0, 10) + '...'),
        currentDevice: deviceId.substring(0, 10) + '...'
      });
    }

    // تسجيل محاولة الدخول الناجحة
    try {
      await prisma.loginAttempt.create({
        data: {
          userId: user.id,
          ipAddress: clientIP,
          deviceId: deviceId,
          success: true,
          userType: 'user',
          timestamp: new Date()
        }
      });
    } catch (loginAttemptError) {
      writeLog(`⚠️ Failed to log login attempt: ${loginAttemptError.message}`);
    }

    // إنشاء token بسيط للجلسة
    const token = `token_${user.id}_${Date.now()}`;

    writeLog(`✅ Login successful for: ${loginName} from authorized device`);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions
      },
      token: token
    });

  } catch (error) {
    writeLog(`❌ Login error: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

// External API: Health Check
app.get('/api/external/health', async (req, res) => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    res.json({
      status: 'success',
      message: 'External API is healthy',
      data: {
        timestamp: new Date().toISOString(),
        database: 'connected',
        version: '1.0.0'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'External API health check failed',
      error_code: 'HEALTH_CHECK_FAILED'
    });
  }
});

// External API: Direct Verification
app.post('/api/external/verify-direct', async (req, res) => {
  try {
    const {
      agent_login_name,
      agent_login_password,
      client_code,
      client_token
    } = req.body;

    writeLog(`🔐 External verify-direct: agent=${agent_login_name}, client=${client_code}`);

    if (!agent_login_name || !agent_login_password || !client_code || !client_token) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields',
        error_code: 'VALIDATION_ERROR'
      });
    }

    // Verify agent
    const agent = await prisma.agent.findFirst({
      where: {
        loginName: agent_login_name,
        isActive: true
      }
    });

    if (!agent) {
      writeLog(`❌ Agent not found: ${agent_login_name}`);
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    const isPasswordValid = await bcrypt.compare(agent_login_password, agent.loginPassword);

    if (!isPasswordValid) {
      writeLog(`❌ Invalid agent password: ${agent_login_name}`);
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    // Verify client (البحث عن العميل بغض النظر عن الحالة)
    const client = await prisma.client.findFirst({
      where: {
        clientCode: parseInt(client_code)
      }
    });

    if (!client) {
      writeLog(`❌ Client not found: ${client_code}`);
      return res.status(404).json({
        status: 'client_error'
      });
    }

    // Check if client_token matches password or token field
    let isTokenValid = false;

    // First try direct comparison with token field
    if (md5Hash(client.token) === client_token) {
      isTokenValid = true;
    }
    // Then try bcrypt comparison with password field
    else if (client.password && client.password.startsWith('$2b$')) {
      try {
        isTokenValid = await bcrypt.compare(client_token, client.password);
      } catch (error) {
        console.error('Bcrypt comparison error:', error);
      }
    }
    // Finally try direct comparison with password field
    else if (client.password === client_token) {
      isTokenValid = true;
    }

    if (!isTokenValid) {
      writeLog(`❌ Invalid client token: ${client_code}`);
      return res.status(401).json({
        status: 'client_error'
      });
    }

    // Log successful operation
    await prisma.dataRecord.create({
      data: {
        agentId: agent.id,
        clientId: client.id,
        clientCode: client_code.toString(),
        clientPassword: client_token,
        operationDate: new Date(),
        operationStatus: 1,
        agentReference: Math.floor(Date.now() / 1000),
        clientIpAddress: req.ip || 'unknown'
      }
    });

    writeLog(`✅ External verify-direct successful: agent=${agent_login_name}, client=${client_code}, status=${client.status}`);

    res.json({
      status: 'success',
      client_status: client.status
    });

  } catch (error) {
    writeLog(`❌ External verify-direct error: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Verification service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// Dashboard Stats API
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    writeLog('📊 Dashboard stats request');

    const [totalUsers, totalClients, totalAgents, totalDataRecords] = await Promise.all([
      prisma.user.count(),
      prisma.client.count(),
      prisma.agent.count(),
      prisma.dataRecord.count()
    ]);

    const stats = {
      totalUsers,
      totalClients,
      totalAgents,
      totalDataRecords,
      totalSecurityRecords: 0
    };

    writeLog('✅ Dashboard stats returned');

    res.json(stats);

  } catch (error) {
    writeLog(`❌ Dashboard stats error: ${error.message}`);
    res.status(500).json({
      error: 'فشل في جلب الإحصائيات',
      details: error.message
    });
  }
});
// تحديث عميل
app.put('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { clientName, appName, cardNumber, password, ipAddress, status } = req.body;

    console.log(`🔄 Updating client ${id}:`, { clientName, appName });

    const updateData = {
      clientName,
      appName,
      cardNumber,
      ipAddress,
      status,
      updatedAt: new Date()
    };

    // تشفير كلمة المرور إذا تم تغييرها
    if (password) {
      const bcrypt = require('bcrypt');
      updateData.password = await bcrypt.hash(password, 10);
    }

    const updatedClient = await prisma.client.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        user: { select: { id: true, username: true, loginName: true } }
      }
    });

    console.log(`✅ Client ${id} updated successfully`);
    res.json(updatedClient);
  } catch (error) {
    console.error('Update client error:', error);
    res.status(500).json({ error: 'فشل في تحديث العميل' });
  }
});


// API العملاء
app.get('/api/clients', async (req, res) => {
  try {
    writeLog('👥 Clients request');

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.client.count()
    ]);

    writeLog(`✅ Returned ${clients.length} clients of ${total} total`);

    res.json({
      data: clients,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    writeLog(`❌ Clients error: ${error.message}`);
    res.status(500).json({
      error: 'فشل في جلب العملاء',
      details: error.message
    });
  }
});

// API الوكلاء
app.get('/api/agents', async (req, res) => {
  try {
    writeLog('🤝 Agents request');

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.agent.count()
    ]);

    writeLog(`✅ Returned ${agents.length} agents of ${total} total`);

    res.json({
      data: agents,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    writeLog(`❌ Agents error: ${error.message}`);
    res.status(500).json({
      error: 'فشل في جلب الوكلاء',
      details: error.message
    });
  }
});

// API المستخدمين
app.get('/api/users', async (req, res) => {
  try {
    writeLog('👤 Users request');

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          username: true,
          loginName: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          deviceId: true,
          device1: true
        }
      }),
      prisma.user.count()
    ]);

    writeLog(`✅ Returned ${users.length} users of ${total} total`);

    res.json({
      data: users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    writeLog(`❌ Users error: ${error.message}`);
    res.status(500).json({
      error: 'فشل في جلب المستخدمين',
      details: error.message
    });
  }
});

// API الأنشطة الحديثة
app.get('/api/dashboard/recent-activity', async (req, res) => {
  try {
    writeLog('📊 Recent activity request');

    const recentData = await prisma.dataRecord.findMany({
      take: 10,
      orderBy: { operationDate: 'desc' },
      include: {
        agent: { select: { agentName: true } },
        client: { select: { clientName: true } }
      }
    });

    const activities = recentData.map(record => ({
      id: record.id,
      type: 'client_verification',
      description: `تحقق من العميل ${record.client?.clientName || record.clientCode}`,
      agent: record.agent?.agentName || 'غير محدد',
      status: record.operationStatus === 1 ? 'success' : 'failed',
      timestamp: record.operationDate
    }));

    writeLog(`✅ Returned ${activities.length} recent activities`);

    res.json({
      data: activities
    });

  } catch (error) {
    writeLog(`❌ Recent activity error: ${error.message}`);
    res.status(500).json({
      error: 'فشل في جلب الأنشطة الحديثة',
      details: error.message
    });
  }
});

// API إحصائيات الأمان
app.get('/api/security/stats', async (req, res) => {
  try {
    writeLog('🔒 Security stats request');

    // إحصائيات بسيطة بدون الاعتماد على جدول loginAttempt
    const stats = {
      totalAttempts: 0,
      successfulAttempts: 0,
      failedAttempts: 0,
      recentAttempts: 0,
      successRate: 0
    };

    // محاولة جلب البيانات من جدول loginAttempt إذا كان موجوداً
    try {
      const totalAttempts = await prisma.loginAttempt.count();
      const successfulAttempts = await prisma.loginAttempt.count({ where: { success: true } });
      const failedAttempts = await prisma.loginAttempt.count({ where: { success: false } });

      // محاولة جلب الأنشطة الحديثة
      let recentAttempts = 0;
      try {
        recentAttempts = await prisma.loginAttempt.count({
          where: {
            timestamp: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
            }
          }
        });
      } catch (timeError) {
        // إذا فشل timestamp، جرب createdAt
        try {
          recentAttempts = await prisma.loginAttempt.count({
            where: {
              createdAt: {
                gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
              }
            }
          });
        } catch (createdError) {
          // إذا فشل كلاهما، استخدم 0
          recentAttempts = 0;
        }
      }

      stats.totalAttempts = totalAttempts;
      stats.successfulAttempts = successfulAttempts;
      stats.failedAttempts = failedAttempts;
      stats.recentAttempts = recentAttempts;
      stats.successRate = totalAttempts > 0 ? Math.round((successfulAttempts / totalAttempts) * 100) : 0;

    } catch (dbError) {
      writeLog(`⚠️ LoginAttempt table not accessible: ${dbError.message}`);
      // استخدام القيم الافتراضية
    }

    writeLog('✅ Security stats returned');

    res.json(stats);

  } catch (error) {
    writeLog(`❌ Security stats error: ${error.message}`);
    res.status(500).json({
      error: 'فشل في جلب إحصائيات الأمان',
      details: error.message
    });
  }
});

// API سجلات تسجيل الدخول
app.get('/api/security/login-attempts', async (req, res) => {
  try {
    writeLog('🔒 Login attempts request');

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    try {
      const [loginAttempts, total] = await Promise.all([
        prisma.loginAttempt.findMany({
          skip,
          take: limit,
          orderBy: { timestamp: 'desc' },
          include: {
            user: {
              select: { username: true, loginName: true }
            }
          }
        }).catch(() => []),
        prisma.loginAttempt.count().catch(() => 0)
      ]);

      const formattedAttempts = loginAttempts.map(attempt => ({
        id: attempt.id,
        userId: attempt.userId,
        username: attempt.user?.username || attempt.user?.loginName || 'غير محدد',
        ipAddress: attempt.ipAddress,
        deviceId: attempt.deviceId?.substring(0, 10) + '...' || 'غير محدد',
        success: attempt.success,
        userType: attempt.userType || 'user',
        timestamp: attempt.timestamp,
        status: attempt.success ? 'نجح' : 'فشل'
      }));

      writeLog(`✅ Returned ${formattedAttempts.length} login attempts of ${total} total`);

      res.json({
        data: formattedAttempts,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      });

    } catch (dbError) {
      writeLog(`⚠️ LoginAttempt table not accessible: ${dbError.message}`);

      // إرجاع بيانات فارغة إذا كان الجدول غير متاح
      res.json({
        data: [],
        total: 0,
        page,
        limit,
        totalPages: 0
      });
    }

  } catch (error) {
    writeLog(`❌ Login attempts error: ${error.message}`);
    res.status(500).json({
      error: 'فشل في جلب سجلات تسجيل الدخول',
      details: error.message
    });
  }
});

// Data Records API
app.get('/api/data-records', async (req, res) => {
  try {
    writeLog('📊 Data records request');

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [dataRecords, total] = await Promise.all([
      prisma.dataRecord.findMany({
        skip,
        take: limit,
        orderBy: { operationDate: 'desc' },
        include: {
          agent: { select: { agentName: true } },
          client: { select: { clientName: true } }
        }
      }),
      prisma.dataRecord.count()
    ]);

    const formattedRecords = dataRecords.map(record => ({
      id: record.id,
      agentId: record.agentId,
      agentName: record.agent?.agentName || 'غير محدد',
      clientId: record.clientId,
      clientCode: record.clientCode,
      clientName: record.client?.clientName || 'غير محدد',
      clientPassword: record.clientPassword,
      operationDate: record.operationDate,
      operationStatus: record.operationStatus,
      agentReference: record.agentReference,
      clientIpAddress: record.clientIpAddress
    }));

    writeLog(`✅ Returned ${formattedRecords.length} records of ${total} total`);

    res.json({
      dataRecords: formattedRecords,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    writeLog(`❌ Data records error: ${error.message}`);
    res.status(500).json({
      error: 'فشل في جلب البيانات',
      details: error.message
    });
  }
});

// Static files
app.use(express.static(path.join(__dirname, '../client/dist')));

// React app fallback
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API endpoint not found', path: req.path });
  }
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  writeLog('🚀 API Server Started!');
  writeLog(`📡 Port: ${PORT}`);
  writeLog(`🌐 Local: http://localhost:${PORT}`);
  writeLog(`🌐 Internal: http://**************:${PORT}`);
  writeLog(`🌍 External: http://***********:${PORT}`);
  writeLog('✅ Ready to serve API requests!');
  writeLog('🔧 External APIs available for developers!');

  console.log('🚀 API Server Started!');
  console.log(`📡 Port: ${PORT}`);
  console.log(`🌐 Local: http://localhost:${PORT}`);
  console.log(`🌐 Internal: http://**************:${PORT}`);
  console.log(`🌍 External: http://***********:${PORT}`);
  console.log('✅ Ready to serve API requests!');
  console.log('🔧 External APIs available for developers!');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  writeLog('\n🛑 Shutting down API server...');
  await prisma.$disconnect();
  process.exit(0);
});
