const express = require('express');
const cors = require('cors');
const session = require('express-session');
const cookieParser = require('cookie-parser');
const compression = require('compression');
const morgan = require('morgan');
const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
require('dotenv').config();

const prisma = new PrismaClient();

// استيراد middleware الأمان المتقدم
const {
  helmetConfig,
  generalLimiter,
  loginLimiter,
  sensitiveOperationsLimiter,
  hideServerInfo,
  blockSensitiveFiles,
  preventSQLInjection,
  preventXSS,
  trackSuspiciousActivity,
  addCSPNonce,
  preventPathTraversal,
  hideApplicationErrors
} = require('./middleware/security');

// استيراد حماية Frontend
const {
  protectFrontend,
  createDecoyRoutes,
  hideServerDetails,
  preventFingerprinting
} = require('./middleware/frontend-protection');

// استيراد نظام المراقبة الأمنية
const {
  securityMonitor,
  securityCheck,
  loginMonitor
} = require('./utils/security-monitor');

const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const clientRoutes = require('./routes/clients');
const agentRoutes = require('./routes/agents');
const apiRoutes = require('./routes/api');
const dashboardRoutes = require('./routes/dashboard');
const lookupRoutes = require('./routes/lookup');
const dataRecordsRoutes = require('./routes/dataRecords');
const securitySimpleRoutes = require('./routes/security-simple');

const app = express();
const PORT = process.env.PORT || 8080;

// إنشاء مجلد logs إذا لم يكن موجوداً
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// دالة للكتابة في ملف اللوق
function writeMainLog(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;

  // طباعة في الكونسول
  console.log(`🚀 MAIN: ${message}`);

  // كتابة في ملف
  const logFile = path.join(logsDir, `main-${new Date().toISOString().split('T')[0]}.log`);
  fs.appendFileSync(logFile, logMessage);
}

// Compression for better performance
app.use(compression({
  level: 6, // مستوى الضغط (1-9)
  threshold: 1024, // ضغط الملفات أكبر من 1KB
  filter: (req, res) => {
    // ضغط جميع الاستجابات القابلة للضغط
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  }
}));

// 🔐 تطبيق الأمان المتقدم
app.use(hideServerInfo); // إخفاء معلومات الخادم
app.use(hideServerDetails); // إخفاء تفاصيل الخادم
app.use(helmetConfig); // إعدادات Helmet المتقدمة
app.use(blockSensitiveFiles); // منع الوصول للملفات الحساسة
app.use(trackSuspiciousActivity); // تتبع النشاط المشبوه
app.use(preventPathTraversal); // منع Path Traversal
app.use(preventSQLInjection); // منع SQL Injection
app.use(preventXSS); // منع XSS
app.use(addCSPNonce); // إضافة nonce للـ CSP
app.use(preventFingerprinting); // منع fingerprinting
app.use(protectFrontend); // حماية Frontend
app.use(securityCheck); // فحص الأمان
app.use(loginMonitor); // مراقبة تسجيل الدخول
app.use(generalLimiter); // حد عام للطلبات
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:8080',
    'http://**************:8080',
    'http://************:8080',
    'http://***********:8080', // IP الخارجي الصريح
    `http://${process.env.EXTERNAL_IP}:${process.env.EXTERNAL_PORT}`,
    `http://${process.env.EXTERNAL_IP}:8080`
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Device-ID']
}));

// Rate limiting معطل مؤقتاً لحل مشكلة الملفات الثابتة
// const limiter = rateLimit({
//   windowMs: 15 * 60 * 1000,
//   max: 1000
// });
// app.use('/api', limiter);
// app.use('/auth', limiter);

// Middleware للتسجيل المفصل
app.use((req, res, next) => {
  const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress;
  const userAgent = req.headers['user-agent'] || 'Unknown';
  const referer = req.headers['referer'] || 'Direct';

  writeMainLog(`INCOMING REQUEST: ${req.method} ${req.url}`);
  writeMainLog(`  Client IP: ${clientIP}`);
  writeMainLog(`  User-Agent: ${userAgent.substring(0, 100)}...`);
  writeMainLog(`  Referer: ${referer}`);

  // تسجيل خاص للملفات الثابتة
  if (req.url.includes('/assets/') || req.url.endsWith('.js') || req.url.endsWith('.css')) {
    writeMainLog(`  STATIC FILE REQUEST: ${req.url}`);
  }

  // تسجيل خاص للصفحة الرئيسية
  if (req.url === '/' || req.url === '/index.html') {
    writeMainLog(`  INDEX PAGE REQUEST from ${clientIP}`);
  }

  next();
});

// Logging
app.use(morgan('combined'));

// Session configuration
app.use(cookieParser());
app.use(session({
  secret: process.env.SESSION_SECRET || 'yemclient-super-secret-key-2024',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false, // HTTP للتطوير
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 ساعة
  }
}));

// Body parsing مع حدود أمان
app.use(express.json({
  limit: '1mb', // تقليل الحد لمنع DoS
  strict: true
}));
app.use(express.urlencoded({
  extended: true,
  limit: '1mb'
}));

// مسارات الأمان (قبل باقي المسارات)
app.get('/api/security/stats', (req, res) => {
  console.log('📊 Security stats requested');
  const stats = securityMonitor.getSecurityStats();
  res.json({
    ...stats,
    lastSecurityScan: new Date().toISOString()
  });
});

app.get('/api/security/login-attempts', (req, res) => {
  console.log('📋 Login attempts requested');
  res.json({
    attempts: [
      {
        id: 1,
        type: 'success',
        username: 'hash8080',
        ip: '*************',
        userAgent: 'Browser',
        timestamp: new Date().toISOString(),
        deviceId: 'honbi5nms_1751046183491',
        reason: null
      }
    ],
    total: 1,
    page: 1,
    limit: 20,
    totalPages: 1
  });
});

// Routes مع limiters أمان
app.use('/api/auth', loginLimiter, authRoutes); // مسارات المصادقة مع حد صارم
app.use('/api/users', sensitiveOperationsLimiter, userRoutes); // API للمستخدمين
app.use('/api/clients', sensitiveOperationsLimiter, clientRoutes); // API للعملاء
app.use('/api/agents', sensitiveOperationsLimiter, agentRoutes); // API للوكلاء
app.use('/api/external', apiRoutes); // API للوكلاء
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/lookup', lookupRoutes); // API للبحث والاختيار
app.use('/api/data-records', dataRecordsRoutes); // API لجدول البيانات
app.use('/api/security', securitySimpleRoutes); // API للأمان

// خدمة الملفات الثابتة للعميل مع cache headers محسنة
const clientDistPath = path.join(__dirname, '../client/dist');
app.use(express.static(clientDistPath, {
  maxAge: '1d', // cache للملفات الثابتة لمدة يوم
  etag: true,
  lastModified: true,
  setHeaders: (res, filePath) => {
    writeMainLog(`SERVING STATIC FILE: ${filePath}`);

    // إعداد MIME types صحيحة
    if (filePath.endsWith('.js')) {
      res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // سنة واحدة
    } else if (filePath.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css; charset=utf-8');
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // سنة واحدة
    } else if (filePath.endsWith('.html')) {
      res.setHeader('Content-Type', 'text/html; charset=utf-8');
      res.setHeader('Cache-Control', 'public, max-age=0, must-revalidate'); // لا cache للHTML
    } else if (filePath.includes('/assets/')) {
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // سنة واحدة
    }

    // إضافة headers للأمان
    res.setHeader('X-Content-Type-Options', 'nosniff');
  }
}));

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});





// Database connection test
app.get('/test-db', async (req, res) => {
  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();

  try {
    writeMainLog(`Testing database connection from IP: ${req.ip || req.connection.remoteAddress}`);
    writeMainLog(`DATABASE_URL: ${process.env.DATABASE_URL}`);

    // اختبار الاتصال
    await prisma.$connect();
    writeMainLog('✅ Database connected successfully');

    // عد المستخدمين
    const userCount = await prisma.user.count();
    writeMainLog(`📊 Users in database: ${userCount}`);

    // البحث عن admin
    const adminUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: 'admin' },
          { loginName: 'admin' }
        ]
      }
    });

    writeMainLog(`👤 Admin user found: ${!!adminUser}`);

    res.json({
      success: true,
      userCount,
      adminExists: !!adminUser,
      message: 'Database connection successful',
      clientIP: req.ip || req.connection.remoteAddress,
      databaseUrl: process.env.DATABASE_URL ? 'Set' : 'Not set'
    });

  } catch (error) {
    writeMainLog(`❌ Database connection failed: ${error.message}`);
    writeMainLog(`❌ Full error: ${error}`);

    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Database connection failed',
      clientIP: req.ip || req.connection.remoteAddress,
      databaseUrl: process.env.DATABASE_URL ? 'Set' : 'Not set'
    });
  } finally {
    await prisma.$disconnect();
  }
});

// Test page
app.get('/test', (req, res) => {
  res.sendFile(path.join(__dirname, 'test.html'));
});

// Connection info API
app.get('/api/connection-info', (req, res) => {
  res.json({
    ip: req.ip || req.connection.remoteAddress,
    port: req.get('host'),
    timestamp: new Date().toISOString(),
    userAgent: req.get('User-Agent')
  });
});

// API test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'API يعمل بشكل صحيح',
    timestamp: new Date().toISOString(),
    server: 'Internal Server (127.0.0.1:8080)'
  });
});

// External access test endpoint
app.get('/api/external-test', (req, res) => {
  res.json({
    message: 'External access working',
    timestamp: new Date().toISOString(),
    server: 'External Server (***********:8080)',
    clientIP: req.ip || req.connection.remoteAddress,
    host: req.get('host')
  });
});

// External API routes (for agents)
app.use('/api', apiRoutes);

// إرجاع index.html لجميع المسارات غير API (للتوجيه من جانب العميل)
app.get('*', (req, res, next) => {
  // تجاهل مسارات API وendpoints خاصة
  if (req.path.startsWith('/api') || req.path.startsWith('/auth') || req.path === '/health' || req.path === '/test-db') {
    return next();
  }

  // تجاهل الملفات الثابتة (JS, CSS, images, fonts, etc.)
  const staticExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot'];
  const hasStaticExtension = staticExtensions.some(ext => req.path.toLowerCase().endsWith(ext));

  if (hasStaticExtension || req.path.startsWith('/assets/')) {
    return next(); // دع express.static يتعامل مع الملفات الثابتة
  }

  writeMainLog(`SERVING INDEX.HTML for route: ${req.path}`);

  // إعداد headers لمنع cache للHTML
  res.setHeader('Content-Type', 'text/html; charset=utf-8');
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.setHeader('X-Content-Type-Options', 'nosniff');

  res.sendFile(path.join(clientDistPath, 'index.html'), (err) => {
    if (err) {
      writeMainLog(`ERROR serving index.html: ${err.message}`);
      console.error('Error serving index.html:', err);
      res.status(500).send('Internal Server Error');
    }
  });
});

// Error handling middleware متقدم
app.use(hideApplicationErrors);

// 404 handler for API routes only
app.use('/api/*', (req, res) => {
  res.status(404).json({ error: 'API route not found' });
});

app.use('/auth/*', (req, res) => {
  res.status(404).json({ error: 'Auth route not found' });
});

// إنشاء مسارات وهمية للإرباك
createDecoyRoutes(app);

app.listen(PORT, '0.0.0.0', () => {
  writeMainLog(`MAIN SERVER STARTED on 0.0.0.0:${PORT}`);
  writeMainLog(`Environment: ${process.env.NODE_ENV}`);
  writeMainLog(`External access: http://${process.env.EXTERNAL_IP}:${PORT}`);
  writeMainLog(`Logs directory: ${logsDir}`);

  console.log(`🚀 Server running on http://0.0.0.0:${PORT}`);
  console.log(`🌐 External access: http://${process.env.EXTERNAL_IP}:${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV}`);
  console.log(`📝 Logs saved to: ${logsDir}`);
});
