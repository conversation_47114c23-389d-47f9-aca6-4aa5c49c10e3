const express = require('express');
const cors = require('cors');
const path = require('path');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

// ========================================
// إعداد التطبيق والمتغيرات
// ========================================

const app = express();
const PORT = process.env.PORT || 8080;

console.log('🚀 Starting Yemen Client Management System...');
console.log(`📡 Server will run on port: ${PORT}`);
console.log(`🌐 External access: http://***********:${PORT}`);

// إعداد Prisma مع معالجة الأخطاء المحسنة
let prisma;
try {
  prisma = new PrismaClient({
    log: ['error', 'warn'],
    errorFormat: 'pretty'
  });
} catch (error) {
  console.error('❌ Prisma initialization error:', error);
  process.exit(1);
}

// ========================================
// معالجة الأخطاء العامة
// ========================================

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  // لا نوقف الخادم، فقط نسجل الخطأ
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  // لا نوقف الخادم، فقط نسجل الخطأ
});

// إغلاق آمن للتطبيق
process.on('SIGINT', async () => {
  console.log('\n🔄 Shutting down gracefully...');
  try {
    await prisma.$disconnect();
    console.log('✅ Database disconnected');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
});

// ========================================
// إعدادات الحماية والأمان
// ========================================

// Helmet للحماية الأساسية
const helmetConfig = helmet({
  hidePoweredBy: true,
  frameguard: { action: 'deny' },
  noSniff: true,
  xssFilter: true,
  referrerPolicy: { policy: 'no-referrer' },
  contentSecurityPolicy: false // تعطيل CSP لتجنب مشاكل الواجهة الأمامية
});

// Rate Limiting مع إعدادات متوازنة
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: false,
    legacyHeaders: false,
    skip: (req) => {
      // تخطي Rate Limiting للطلبات المحلية في التطوير
      const isLocal = req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === '::ffff:127.0.0.1';
      return isLocal && process.env.NODE_ENV !== 'production';
    }
  });
};

const generalLimiter = createRateLimiter(15 * 60 * 1000, 200, 'تم تجاوز الحد المسموح من الطلبات');
const loginLimiter = createRateLimiter(15 * 60 * 1000, 10, 'تم تجاوز محاولات تسجيل الدخول المسموحة');
const apiLimiter = createRateLimiter(5 * 60 * 1000, 50, 'تم تجاوز الحد المسموح للعمليات الحساسة');

// ========================================
// Middleware الأساسي
// ========================================

app.use(helmetConfig);
app.use(generalLimiter);

// إخفاء معلومات الخادم
app.use((req, res, next) => {
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');
  res.setHeader('Server', 'Apache/2.4.41');
  res.setHeader('X-Powered-By', 'PHP/7.4.3');
  next();
});

// CORS مع إعدادات مرنة وآمنة
const corsOptions = {
  origin: function (origin, callback) {
    // السماح بجميع الأصول في التطوير
    if (!origin || process.env.NODE_ENV !== 'production') {
      return callback(null, true);
    }

    // قائمة الأصول المسموحة في الإنتاج
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:8080',
      'http://***********:8080',
      'http://**************:8080'
    ];

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('غير مسموح بواسطة CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Device-ID']
};

app.use(cors(corsOptions));

// Body parsing مع حدود آمنة
app.use(express.json({
  limit: '10mb',
  verify: (req, res, buf) => {
    try {
      JSON.parse(buf);
    } catch (e) {
      res.status(400).json({ error: 'Invalid JSON format' });
      throw new Error('Invalid JSON');
    }
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging آمن ومفيد
app.use((req, res, next) => {
  const clientIP = req.headers['x-forwarded-for'] ||
                   req.connection.remoteAddress ||
                   req.socket.remoteAddress ||
                   req.ip;

  const timestamp = new Date().toISOString();
  const userAgent = req.get('User-Agent') || 'Unknown';

  console.log(`${timestamp} - ${req.method} ${req.path} from ${clientIP} - ${userAgent.substring(0, 50)}`);
  next();
});

// ========================================
// Middleware الحماية المتقدمة
// ========================================

// منع الوصول للملفات الحساسة
app.use((req, res, next) => {
  const blockedPaths = [
    '/.env', '/package.json', '/package-lock.json', '/node_modules',
    '/.git', '/prisma', '/logs', '/config', '/.vscode', '/src',
    '/server', '/.gitignore', '/README.md', '/middleware', '/routes'
  ];

  const isBlocked = blockedPaths.some(blocked =>
    req.path.toLowerCase().includes(blocked.toLowerCase())
  );

  if (isBlocked) {
    console.log(`🚨 محاولة وصول لملف حساس: ${req.path} من IP: ${clientIP}`);
    return res.status(404).json({ error: 'الصفحة غير موجودة' });
  }
  next();
});

// منع SQL Injection
app.use((req, res, next) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /(\'|\"|;|--|\*|\|)/,
    /(\bUNION\b.*\bSELECT\b)/i,
    /(\bINSERT\b.*\bINTO\b)/i,
    /(\bDROP\b.*\bTABLE\b)/i
  ];

  const checkForSQLInjection = (obj) => {
    if (!obj || typeof obj !== 'object') return false;

    for (let key in obj) {
      if (typeof obj[key] === 'string') {
        for (let pattern of sqlPatterns) {
          if (pattern.test(obj[key])) {
            console.log(`🚨 محاولة SQL Injection من IP: ${req.ip} - Pattern: ${obj[key]}`);
            return true;
          }
        }
      } else if (typeof obj[key] === 'object') {
        if (checkForSQLInjection(obj[key])) return true;
      }
    }
    return false;
  };

  if (checkForSQLInjection(req.query) || checkForSQLInjection(req.body)) {
    return res.status(400).json({ error: 'بيانات غير صحيحة' });
  }
  next();
});

// تتبع النشاط المشبوه
app.use((req, res, next) => {
  const suspiciousAgents = [
    /sqlmap/i, /nikto/i, /nmap/i, /masscan/i, /zap/i, /burp/i,
    /curl/i, /wget/i, /python-requests/i
  ];

  const userAgent = req.get('User-Agent') || '';

  if (suspiciousAgents.some(pattern => pattern.test(userAgent))) {
    console.log(`🚨 User Agent مشبوه: ${userAgent} من IP: ${req.ip}`);
    return res.status(403).json({ error: 'وصول مرفوض' });
  }
  next();
});

// ========================================
// JWT والمصادقة
// ========================================

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key-change-in-production-' + Date.now();

// Middleware للتحقق من التوكن
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    const deviceId = req.headers['x-device-id'];

    // المسارات العامة
    const publicPaths = [
      '/health', '/api/test', '/api/client/login', '/api/agent/login',
      '/api/external/health', '/api/external/stats', '/api/external/verify-direct'
    ];

    if (publicPaths.includes(req.path)) {
      return next();
    }

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    if (!deviceId) {
      return res.status(401).json({ error: 'Device ID required' });
    }

    const decoded = jwt.verify(token, JWT_SECRET);

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        isActive: true,
        permissions: true
      }
    });

    if (!user || !user.isActive) {
      return res.status(401).json({ error: 'User not found or inactive' });
    }

    // التحقق من Device ID (مرن)
    if (user.deviceId && user.deviceId !== deviceId) {
      await prisma.user.update({
        where: { id: user.id },
        data: { deviceId: deviceId }
      });
      console.log(`📱 Device ID updated for user: ${user.loginName}`);
    }

    req.user = user;
    req.deviceId = deviceId;
    next();

  } catch (error) {
    console.error('Authentication error:', error);

    if (error.name === 'JsonWebTokenError') {
      return res.status(403).json({ error: 'Invalid token' });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(403).json({ error: 'Token expired' });
    } else {
      return res.status(500).json({ error: 'Authentication error' });
    }
  }
};

// ========================================
// اختبار الاتصال بقاعدة البيانات
// ========================================

const testDatabaseConnection = async () => {
  let retries = 5;
  while (retries > 0) {
    try {
      await prisma.$connect();
      console.log('✅ Database connected successfully');

      const [userCount, clientCount, agentCount] = await Promise.all([
        prisma.user.count().catch(() => 0),
        prisma.client.count().catch(() => 0),
        prisma.agent.count().catch(() => 0)
      ]);

      console.log(`📊 Database stats: Users: ${userCount}, Clients: ${clientCount}, Agents: ${agentCount}`);
      return true;
    } catch (error) {
      console.error(`❌ Database connection failed (${retries} retries left):`, error.message);
      retries--;
      if (retries > 0) {
        console.log('🔄 Retrying database connection in 2 seconds...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }
  console.error('❌ Failed to connect to database after all retries');
  return false;
};

// ========================================
// APIs الأساسية
// ========================================

// Health check محسن
app.get('/health', async (req, res) => {
  try {
    const startTime = Date.now();

    await prisma.$queryRaw`SELECT 1`;
    const dbResponseTime = Date.now() - startTime;

    const [userCount, clientCount, agentCount] = await Promise.all([
      prisma.user.count().catch(() => 0),
      prisma.client.count().catch(() => 0),
      prisma.agent.count().catch(() => 0)
    ]);

    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      database: {
        status: 'connected',
        responseTime: `${dbResponseTime}ms`,
        users: userCount,
        clients: clientCount,
        agents: agentCount
      },
      server: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version
      }
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: 'Database connection failed'
    });
  }
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'API يعمل بشكل صحيح',
    timestamp: new Date().toISOString(),
    server: 'Yemen Client Management System',
    version: '2.0.0',
    status: 'operational'
  });
});

// ========================================
// APIs تسجيل الدخول
// ========================================

// User login
app.post('/api/user/login', loginLimiter, async (req, res) => {
  try {
    const { loginName, password, deviceId } = req.body;
    console.log('🔐 User Login attempt:', { loginName, deviceId });

    if (!loginName || !password || !deviceId) {
      return res.status(400).json({
        success: false,
        message: 'جميع الحقول مطلوبة'
      });
    }

    const user = await prisma.user.findUnique({
      where: { loginName },
      select: {
        id: true,
        username: true,
        loginName: true,
        password: true,
        deviceId: true,
        isActive: true,
        permissions: true
      }
    });

    if (!user) {
      console.log('❌ User not found:', loginName);
      return res.status(401).json({
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    if (!user.isActive) {
      console.log('❌ User inactive:', loginName);
      return res.status(401).json({
        success: false,
        message: 'الحساب غير نشط'
      });
    }

    const passwordValid = await bcrypt.compare(password, user.password);
    if (!passwordValid) {
      console.log('❌ Invalid password for user:', loginName);
      return res.status(401).json({
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    // تحديث Device ID
    if (!user.deviceId || user.deviceId !== deviceId) {
      await prisma.user.update({
        where: { id: user.id },
        data: { deviceId: deviceId }
      });
    }

    // إنشاء JWT Token
    const token = jwt.sign(
      { userId: user.id, loginName: user.loginName },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    console.log('✅ User login successful:', loginName);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions
      },
      token: token
    });

  } catch (error) {
    console.error('User login error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

// Client login
app.post('/api/client/login', loginLimiter, async (req, res) => {
  try {
    const { clientCode, password } = req.body;
    console.log('🏢 Client Login attempt:', { clientCode });

    if (!clientCode || !password) {
      return res.status(400).json({
        success: false,
        message: 'رمز العميل وكلمة المرور مطلوبان'
      });
    }

    const client = await prisma.client.findUnique({
      where: { clientCode: parseInt(clientCode) }
    });

    if (!client) {
      console.log('❌ Client not found:', clientCode);
      return res.status(401).json({
        success: false,
        message: 'رمز العميل أو كلمة المرور غير صحيحة'
      });
    }

    if (client.status !== 1) {
      console.log('❌ Client inactive:', clientCode);
      return res.status(401).json({
        success: false,
        message: 'حساب العميل غير نشط'
      });
    }

    const passwordValid = await bcrypt.compare(password, client.password);
    if (!passwordValid) {
      console.log('❌ Invalid password for client:', clientCode);
      return res.status(401).json({
        success: false,
        message: 'رمز العميل أو كلمة المرور غير صحيحة'
      });
    }

    console.log('✅ Client login successful:', clientCode);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      client: {
        id: client.id,
        clientCode: client.clientCode,
        clientName: client.clientName,
        token: client.token,
        appName: client.appName,
        ipAddress: client.ipAddress,
        status: client.status
      }
    });

  } catch (error) {
    console.error('Client login error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

// ========================================
// APIs البيانات المحمية
// ========================================

// Users API
app.get('/api/users', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (page - 1) * limit;

    const where = search ? {
      OR: [
        { username: { contains: search, mode: 'insensitive' } },
        { loginName: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        select: {
          id: true,
          username: true,
          loginName: true,
          deviceId: true,
          permissions: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          _count: { select: { clients: true } }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    console.log(`✅ Users API: Retrieved ${users.length} users for user ${req.user.id}`);
    res.json({
      data: users,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Users error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// Clients API
app.get('/api/clients', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, userId, search = '' } = req.query;
    const skip = (page - 1) * limit;

    let where = {};
    if (userId) {
      where.userId = parseInt(userId);
    }
    if (search) {
      where.OR = [
        { clientName: { contains: search, mode: 'insensitive' } },
        { appName: { contains: search, mode: 'insensitive' } }
      ];
    }

    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          user: { select: { id: true, username: true, loginName: true } }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.client.count({ where })
    ]);

    console.log(`✅ Clients API: Retrieved ${clients.length} clients for user ${req.user.id}`);
    res.json({
      data: clients,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Clients error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// Agents API
app.get('/api/agents', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (page - 1) * limit;

    const where = search ? {
      OR: [
        { agentName: { contains: search, mode: 'insensitive' } },
        { agencyName: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        select: {
          id: true,
          agentName: true,
          agencyName: true,
          agencyType: true,
          ipAddress: true,
          loginName: true,
          isActive: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.agent.count({ where })
    ]);

    console.log(`✅ Agents API: Retrieved ${agents.length} agents for user ${req.user.id}`);
    res.json({
      data: agents,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Agents error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// Data Records API
app.get('/api/data-records', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, agentId, clientId } = req.query;
    const skip = (page - 1) * limit;

    let where = {};
    if (agentId) where.agentId = parseInt(agentId);
    if (clientId) where.clientId = parseInt(clientId);

    const [dataRecords, total] = await Promise.all([
      prisma.dataRecord.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          agent: { select: { agentName: true } },
          client: { select: { clientName: true } }
        },
        orderBy: { operationDate: 'desc' }
      }),
      prisma.dataRecord.count({ where })
    ]);

    console.log(`✅ Data Records API: Retrieved ${dataRecords.length} records for user ${req.user.id}`);
    res.json({
      data: dataRecords,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Data records error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// ========================================
// APIs المطورين (External APIs)
// ========================================

// API للتحقق المباشر
app.post('/api/external/verify-direct', async (req, res) => {
  try {
    const { client_code, password } = req.body;

    console.log('🔍 Direct verification request:', { client_code });

    if (!client_code || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: client_code and password',
        error_code: 'VALIDATION_ERROR'
      });
    }

    const client = await prisma.client.findUnique({
      where: { clientCode: parseInt(client_code) }
    });

    if (!client) {
      return res.status(404).json({
        status: 'client_not_found',
        message: 'Client not found'
      });
    }

    if (client.status !== 1) {
      return res.status(403).json({
        status: 'client_inactive',
        message: 'Client account is inactive'
      });
    }

    const passwordValid = await bcrypt.compare(password, client.password);
    if (!passwordValid) {
      return res.status(401).json({
        status: 'invalid_credentials',
        message: 'Invalid password'
      });
    }

    res.json({
      status: 'success',
      message: 'Client verified successfully',
      data: {
        client_code: client.clientCode,
        client_name: client.clientName,
        app_name: client.appName,
        status: client.status,
        token: client.token
      }
    });

  } catch (error) {
    console.error('Direct verification error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Verification service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// API لإحصائيات سريعة
app.get('/api/external/stats', async (req, res) => {
  try {
    const [totalClients, activeClients, totalAgents, activeAgents] = await Promise.all([
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.agent.count(),
      prisma.agent.count({ where: { isActive: true } })
    ]);

    res.json({
      status: 'success',
      data: {
        total_clients: totalClients,
        active_clients: activeClients,
        blocked_clients: totalClients - activeClients,
        total_agents: totalAgents,
        active_agents: activeAgents,
        inactive_agents: totalAgents - activeAgents,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Stats error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Stats service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// API لفحص حالة النظام
app.get('/api/external/health', async (req, res) => {
  try {
    await prisma.$connect();
    const userCount = await prisma.user.count();

    res.json({
      status: 'success',
      message: 'System is healthy',
      data: {
        database: 'connected',
        users: userCount,
        server: 'operational',
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'System health check failed',
      error_code: 'HEALTH_CHECK_ERROR'
    });
  }
});

// ========================================
// Static Files والتشغيل
// ========================================

// Static files
app.use(express.static(path.join(__dirname, '../client/dist')));

// React app fallback
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API not found' });
  }
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('خطأ في التطبيق:', err);
  res.status(500).json({
    error: 'حدث خطأ في الخادم',
    timestamp: new Date().toISOString()
  });
});

// ========================================
// تشغيل الخادم
// ========================================

const startServer = async () => {
  try {
    // اختبار قاعدة البيانات أولاً
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
      console.error('❌ Cannot start server without database connection');
      process.exit(1);
    }

    // تشغيل الخادم
    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log('\n========================================');
      console.log('✅ Yemen Client Management System Started');
      console.log('========================================');
      console.log(`🌐 External: http://***********:${PORT}`);
      console.log(`🏠 Local: http://localhost:${PORT}`);
      console.log(`🔐 Security: ENABLED`);
      console.log(`🛡️ Rate Limiting: ACTIVE`);
      console.log(`🚨 Threat Protection: ACTIVE`);
      console.log(`📊 Database: CONNECTED`);
      console.log('========================================\n');
    });

    // معالجة إغلاق الخادم بأمان
    const gracefulShutdown = async (signal) => {
      console.log(`\n🔄 Received ${signal}. Shutting down gracefully...`);

      server.close(async () => {
        console.log('✅ HTTP server closed');

        try {
          await prisma.$disconnect();
          console.log('✅ Database disconnected');
          process.exit(0);
        } catch (error) {
          console.error('❌ Error during shutdown:', error);
          process.exit(1);
        }
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// بدء التشغيل
startServer();
