const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * 🔐 نظام الحماية المتقدم - الطبقة الأولى
 * يتحقق من التوكن والجهاز والصلاحيات
 */
class AdvancedSecurity {
  
  /**
   * التحقق من صحة JWT Token مع التشفير المتقدم
   */
  static async verifySecureToken(req, res, next) {
    try {
      const authHeader = req.headers['authorization'];
      const token = authHeader && authHeader.split(' ')[1];
      const deviceId = req.headers['x-device-id'];
      const userAgent = req.headers['user-agent'];
      const clientIP = req.ip || req.connection.remoteAddress;

      // التحقق من وجود التوكن
      if (!token) {
        return res.status(401).json({
          error: 'UNAUTHORIZED',
          message: 'Access token required',
          code: 'MISSING_TOKEN',
          timestamp: new Date().toISOString()
        });
      }

      // التحقق من وجود Device ID
      if (!deviceId) {
        return res.status(401).json({
          error: 'UNAUTHORIZED',
          message: 'Device ID required',
          code: 'MISSING_DEVICE_ID',
          timestamp: new Date().toISOString()
        });
      }

      // فك تشفير التوكن
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

      // التحقق من وجود المستخدم
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          username: true,
          loginName: true,
          deviceId: true,
          permissions: true,
          isActive: true,
          lastLoginAt: true,
          ipAddress: true
        }
      });

      if (!user || !user.isActive) {
        await AdvancedSecurity.logSecurityEvent('INVALID_USER', {
          userId: decoded.userId,
          ip: clientIP,
          userAgent,
          deviceId
        });

        return res.status(401).json({
          error: 'UNAUTHORIZED',
          message: 'User not found or inactive',
          code: 'INVALID_USER',
          timestamp: new Date().toISOString()
        });
      }

      // التحقق من Device ID
      if (user.deviceId) {
        const allowedDevices = user.deviceId.includes(',')
          ? user.deviceId.split(',').map(id => id.trim())
          : [user.deviceId];

        if (!allowedDevices.includes(deviceId)) {
          await AdvancedSecurity.logSecurityEvent('DEVICE_MISMATCH', {
            userId: user.id,
            expectedDevices: allowedDevices,
            actualDevice: deviceId,
            ip: clientIP,
            userAgent
          });

          return res.status(403).json({
            error: 'FORBIDDEN',
            message: 'Device not authorized',
            code: 'DEVICE_MISMATCH',
            timestamp: new Date().toISOString()
          });
        }
      }

      // التحقق من IP Address (اختياري)
      if (user.ipAddress && user.ipAddress !== clientIP) {
        await AdvancedSecurity.logSecurityEvent('IP_MISMATCH', {
          userId: user.id,
          expectedIP: user.ipAddress,
          actualIP: clientIP,
          deviceId,
          userAgent
        });
      }

      // إضافة بيانات المستخدم للطلب
      req.user = user;
      req.deviceId = deviceId;
      req.clientIP = clientIP;
      req.securityContext = {
        tokenIssued: new Date(decoded.iat * 1000),
        tokenExpires: new Date(decoded.exp * 1000),
        verified: true
      };

      next();
    } catch (error) {
      await AdvancedSecurity.logSecurityEvent('TOKEN_ERROR', {
        error: error.message,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        deviceId: req.headers['x-device-id']
      });

      return res.status(403).json({
        error: 'FORBIDDEN',
        message: 'Invalid or expired token',
        code: 'TOKEN_ERROR',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * التحقق من الصلاحيات
   */
  static checkPermission(resource, action) {
    return (req, res, next) => {
      try {
        if (!req.user) {
          return res.status(401).json({
            error: 'UNAUTHORIZED',
            message: 'Authentication required',
            code: 'NO_AUTH',
            timestamp: new Date().toISOString()
          });
        }

        const permissions = req.user.permissions || {};
        const resourcePermissions = permissions[resource];

        if (!resourcePermissions || !resourcePermissions[action]) {
          AdvancedSecurity.logSecurityEvent('PERMISSION_DENIED', {
            userId: req.user.id,
            resource,
            action,
            ip: req.clientIP,
            deviceId: req.deviceId
          });

          return res.status(403).json({
            error: 'FORBIDDEN',
            message: 'Insufficient permissions',
            code: 'PERMISSION_DENIED',
            resource,
            action,
            timestamp: new Date().toISOString()
          });
        }

        next();
      } catch (error) {
        return res.status(500).json({
          error: 'INTERNAL_ERROR',
          message: 'Permission check failed',
          code: 'PERMISSION_CHECK_ERROR',
          timestamp: new Date().toISOString()
        });
      }
    };
  }

  /**
   * حماية من الهجمات المتقدمة
   */
  static advancedThreatProtection(req, res, next) {
    const userAgent = req.headers['user-agent'] || '';
    const clientIP = req.ip || req.connection.remoteAddress;
    const path = req.path;
    const method = req.method;

    // قائمة User Agents المشبوهة
    const suspiciousAgents = [
      /sqlmap/i, /nikto/i, /nmap/i, /masscan/i, /zap/i, /burp/i,
      /curl/i, /wget/i, /python/i, /postman/i, /insomnia/i,
      /bot/i, /crawler/i, /spider/i, /scraper/i
    ];

    // قائمة المسارات المحظورة
    const blockedPaths = [
      /admin/i, /phpmyadmin/i, /wp-admin/i, /\.php$/i, /\.asp$/i,
      /\.jsp$/i, /config/i, /backup/i, /database/i, /\.sql$/i,
      /\.bak$/i, /\.env$/i, /\.git/i, /\.svn/i
    ];

    // فحص User Agent
    if (suspiciousAgents.some(pattern => pattern.test(userAgent))) {
      AdvancedSecurity.logSecurityEvent('SUSPICIOUS_USER_AGENT', {
        userAgent,
        ip: clientIP,
        path,
        method
      });

      return res.status(403).json({
        error: 'FORBIDDEN',
        message: 'Access denied',
        code: 'SUSPICIOUS_ACTIVITY',
        timestamp: new Date().toISOString()
      });
    }

    // فحص المسار
    if (blockedPaths.some(pattern => pattern.test(path))) {
      AdvancedSecurity.logSecurityEvent('BLOCKED_PATH_ACCESS', {
        path,
        ip: clientIP,
        userAgent,
        method
      });

      return res.status(404).json({
        error: 'NOT_FOUND',
        message: 'Resource not found',
        code: 'BLOCKED_PATH',
        timestamp: new Date().toISOString()
      });
    }

    next();
  }

  /**
   * تسجيل الأحداث الأمنية
   */
  static async logSecurityEvent(eventType, details) {
    try {
      const logEntry = {
        timestamp: new Date().toISOString(),
        eventType,
        details: JSON.stringify(details),
        severity: AdvancedSecurity.getEventSeverity(eventType)
      };

      console.log(`🚨 SECURITY EVENT [${eventType}]:`, details);

      // حفظ في قاعدة البيانات إذا كان الجدول موجود
      try {
        await prisma.securityLog.create({
          data: {
            eventType,
            details: JSON.stringify(details),
            ipAddress: details.ip || 'unknown',
            userAgent: details.userAgent || 'unknown',
            userId: details.userId || null,
            severity: logEntry.severity
          }
        });
      } catch (dbError) {
        // إذا لم يكن الجدول موجود، نسجل في الكونسول فقط
        console.log('📝 Security log saved to console only (DB table not available)');
      }
    } catch (error) {
      console.error('❌ Failed to log security event:', error);
    }
  }

  /**
   * تحديد مستوى خطورة الحدث
   */
  static getEventSeverity(eventType) {
    const severityMap = {
      'INVALID_USER': 'HIGH',
      'DEVICE_MISMATCH': 'HIGH',
      'TOKEN_ERROR': 'MEDIUM',
      'PERMISSION_DENIED': 'MEDIUM',
      'SUSPICIOUS_USER_AGENT': 'HIGH',
      'BLOCKED_PATH_ACCESS': 'MEDIUM',
      'IP_MISMATCH': 'LOW'
    };

    return severityMap[eventType] || 'MEDIUM';
  }

  /**
   * تشفير البيانات الحساسة
   */
  static encryptSensitiveData(data) {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(process.env.ENCRYPTION_KEY || 'default-key', 'salt', 32);
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipher(algorithm, key, iv);
    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: cipher.getAuthTag().toString('hex')
    };
  }

  /**
   * فك تشفير البيانات الحساسة
   */
  static decryptSensitiveData(encryptedData) {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(process.env.ENCRYPTION_KEY || 'default-key', 'salt', 32);
    
    const decipher = crypto.createDecipher(algorithm, key, Buffer.from(encryptedData.iv, 'hex'));
    decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return JSON.parse(decrypted);
  }
}

module.exports = AdvancedSecurity;
