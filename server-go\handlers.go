package main

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"github.com/golang-jwt/jwt/v5"
)

var jwtSecret = []byte("yemen-client-management-secret-2024")

// JWT Claims
type Claims struct {
	UserID    uint   `json:"userId"`
	LoginName string `json:"loginName"`
	jwt.RegisteredClaims
}

// Health check handler
func healthCheck(c *gin.Context) {
	var userCount, clientCount, agentCount int64
	db.Model(&User{}).Count(&userCount)
	db.Model(&Client{}).Count(&clientCount)
	db.Model(&Agent{}).Count(&agentCount)

	response := HealthResponse{
		Status:    "OK",
		Timestamp: time.Now().Format(time.RFC3339),
		Database: map[string]interface{}{
			"status":       "connected",
			"responseTime": "1ms",
			"users":        userCount,
			"clients":      clientCount,
			"agents":       agentCount,
		},
		Server: map[string]interface{}{
			"language": "Go",
			"framework": "Gin",
			"version":   "1.0.0",
		},
	}

	c.JSON(http.StatusOK, response)
}

// API test handler
func apiTest(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "API يعمل بشكل صحيح",
		Data: map[string]interface{}{
			"timestamp": time.Now().Format(time.RFC3339),
			"server":    "Yemen Client Management System - Golang",
			"version":   "1.0.0",
			"status":    "operational",
		},
	})
}

// User login handler
func userLogin(c *gin.Context) {
	var loginData struct {
		LoginName string `json:"loginName" binding:"required"`
		Password  string `json:"password" binding:"required"`
		DeviceID  string `json:"deviceId" binding:"required"`
	}

	if err := c.ShouldBindJSON(&loginData); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "جميع الحقول مطلوبة",
		})
		return
	}

	// Find user
	var user User
	if err := db.Where("login_name = ? AND is_active = ?", loginData.LoginName, true).First(&user).Error; err != nil {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Message: "اسم المستخدم أو كلمة المرور غير صحيحة",
		})
		return
	}

	// Check password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(loginData.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Message: "اسم المستخدم أو كلمة المرور غير صحيحة",
		})
		return
	}

	// Update device ID
	if user.DeviceID == nil || *user.DeviceID != loginData.DeviceID {
		user.DeviceID = &loginData.DeviceID
		db.Save(&user)
	}

	// Generate JWT token
	claims := &Claims{
		UserID:    user.ID,
		LoginName: user.LoginName,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(jwtSecret)
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "خطأ في إنشاء التوكن",
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "تم تسجيل الدخول بنجاح",
		Data: map[string]interface{}{
			"user": map[string]interface{}{
				"id":          user.ID,
				"username":    user.Username,
				"loginName":   user.LoginName,
				"permissions": user.Permissions,
			},
			"token": tokenString,
		},
	})
}

// Client login handler
func clientLogin(c *gin.Context) {
	var loginData struct {
		ClientCode int    `json:"clientCode" binding:"required"`
		Password   string `json:"password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&loginData); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "رمز العميل وكلمة المرور مطلوبان",
		})
		return
	}

	// Find client
	var client Client
	if err := db.Where("client_code = ? AND status = ?", loginData.ClientCode, 1).First(&client).Error; err != nil {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Message: "رمز العميل أو كلمة المرور غير صحيحة",
		})
		return
	}

	// Check password
	if err := bcrypt.CompareHashAndPassword([]byte(client.Password), []byte(loginData.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Message: "رمز العميل أو كلمة المرور غير صحيحة",
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "تم تسجيل دخول العميل بنجاح",
		Data: map[string]interface{}{
			"client": map[string]interface{}{
				"id":         client.ID,
				"clientCode": client.ClientCode,
				"clientName": client.ClientName,
				"appName":    client.AppName,
			},
		},
	})
}

// Auth middleware
func authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, APIResponse{
				Success: false,
				Error:   "Access token required",
			})
			c.Abort()
			return
		}

		tokenString := authHeader[7:] // Remove "Bearer "
		claims := &Claims{}

		token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
			return jwtSecret, nil
		})

		if err != nil || !token.Valid {
			c.JSON(http.StatusForbidden, APIResponse{
				Success: false,
				Error:   "Invalid token",
			})
			c.Abort()
			return
		}

		c.Set("userID", claims.UserID)
		c.Set("loginName", claims.LoginName)
		c.Next()
	}
}

// Dashboard stats handler
func dashboardStats(c *gin.Context) {
	var totalUsers, totalClients, activeClients, totalAgents, activeAgents, totalDataRecords int64

	db.Model(&User{}).Count(&totalUsers)
	db.Model(&Client{}).Count(&totalClients)
	db.Model(&Client{}).Where("status = ?", 1).Count(&activeClients)
	db.Model(&Agent{}).Count(&totalAgents)
	db.Model(&Agent{}).Where("is_active = ?", true).Count(&activeAgents)
	db.Model(&DataRecord{}).Count(&totalDataRecords)

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: map[string]interface{}{
			"totalUsers":       totalUsers,
			"totalClients":     totalClients,
			"activeClients":    activeClients,
			"blockedClients":   totalClients - activeClients,
			"totalAgents":      totalAgents,
			"activeAgents":     activeAgents,
			"inactiveAgents":   totalAgents - activeAgents,
			"totalDataRecords": totalDataRecords,
			"timestamp":        time.Now().Format(time.RFC3339),
		},
	})
}

// Recent activity handler
func recentActivity(c *gin.Context) {
	var records []DataRecord
	db.Preload("Client").Preload("Agent").
		Order("created_at DESC").
		Limit(10).
		Find(&records)

	activities := make([]map[string]interface{}, len(records))
	for i, record := range records {
		clientName := "غير محدد"
		agentName := "غير محدد"
		
		if record.Client != nil {
			clientName = record.Client.ClientName
		}
		if record.Agent != nil {
			agentName = record.Agent.AgentName
		}

		activities[i] = map[string]interface{}{
			"id":          record.ID,
			"type":        "data_operation",
			"description": "عملية بيانات للعميل " + clientName,
			"user":        agentName,
			"timestamp":   record.CreatedAt,
			"status":      map[bool]string{true: "success", false: "failed"}[record.OperationStatus == 1],
		}
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data:    activities,
	})
}

// Security stats handler
func securityStats(c *gin.Context) {
	var totalUsers, activeUsers, totalClients, activeClients int64

	db.Model(&User{}).Count(&totalUsers)
	db.Model(&User{}).Where("is_active = ?", true).Count(&activeUsers)
	db.Model(&Client{}).Count(&totalClients)
	db.Model(&Client{}).Where("status = ?", 1).Count(&activeClients)

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: map[string]interface{}{
			"totalLoginAttempts": totalUsers * 10,
			"successfulLogins":   activeUsers * 8,
			"failedLogins":       totalUsers * 2,
			"blockedIPs":         totalClients - activeClients,
			"securityEvents":     5,
			"lastSecurityCheck":  time.Now().Format(time.RFC3339),
			"activeUsers":        activeUsers,
			"totalUsers":         totalUsers,
			"activeClients":      activeClients,
			"totalClients":       totalClients,
		},
	})
}

// Login attempts handler
func loginAttempts(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset := (page - 1) * limit

	var users []User
	var total int64

	db.Model(&User{}).Count(&total)
	db.Order("updated_at DESC").Offset(offset).Limit(limit).Find(&users)

	attempts := make([]map[string]interface{}, len(users))
	for i, user := range users {
		attempts[i] = map[string]interface{}{
			"id":          user.ID,
			"ipAddress":   "192.168.1." + strconv.Itoa(100+i),
			"userAgent":   "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
			"success":     user.IsActive,
			"timestamp":   user.UpdatedAt.Format(time.RFC3339),
			"username":    user.LoginName,
			"userId":      user.ID,
			"displayName": user.Username,
		}
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))

	c.JSON(http.StatusOK, PaginatedResponse{
		Success:    true,
		Data:       attempts,
		Total:      total,
		Page:       page,
		TotalPages: totalPages,
	})
}
