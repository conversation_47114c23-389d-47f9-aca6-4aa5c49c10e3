package main

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// ============== هياكل البيانات (Models) ==============
type User struct {
	ID         uint           `gorm:"primaryKey" json:"id"`
	Username   string         `json:"username"`
	LoginName  string         `gorm:"unique" json:"loginName"`
	Password   string         `json:"-"`
	DeviceID   string         `json:"deviceId"`
	Device1    string         `json:"device1"`
	Permissions Permissions   `gorm:"type:jsonb" json:"permissions"`
	IsActive   bool           `json:"isActive"`
	CreatedAt  time.Time      `json:"createdAt"`
	UpdatedAt  time.Time      `json:"updatedAt"`
	Clients    []Client       `gorm:"foreignKey:UserID" json:"clients"`
}

type Permissions struct {
	IsAdmin     bool `json:"isAdmin"`
	Users       struct {
		Read   bool `json:"read"`
		Create bool `json:"create"`
		Update bool `json:"update"`
		Delete bool `json:"delete"`
	} `json:"users"`
	Clients struct {
		Read   bool `json:"read"`
		Create bool `json:"create"`
		Update bool `json:"update"`
		Delete bool `json:"delete"`
	} `json:"clients"`
	Agents struct {
		Read   bool `json:"read"`
		Create bool `json:"create"`
		Update bool `json:"update"`
		Delete bool `json:"delete"`
	} `json:"agents"`
	DataRecords struct {
		Read   bool `json:"read"`
		Create bool `json:"create"`
		Update bool `json:"update"`
		Delete bool `json:"delete"`
	} `json:"dataRecords"`
	Security struct {
		Read   bool `json:"read"`
		Create bool `json:"create"`
		Update bool `json:"update"`
		Delete bool `json:"delete"`
	} `json:"security"`
	Dashboard struct {
		Read bool `json:"read"`
	} `json:"dashboard"`
}

type Client struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	ClientCode  int       `gorm:"unique" json:"clientCode"`
	ClientName  string    `json:"clientName"`
	AppName     string    `json:"appName"`
	CardNumber  string    `json:"cardNumber"`
	Password    string    `json:"-"`
	Token       string    `json:"token"`
	Status      int       `json:"status"` // 1 = active, 0 = inactive
	IPAddress   string    `json:"ipAddress"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
	UserID      *uint     `json:"userId"`
	User        *User     `json:"user"`
}

type Agent struct {
	ID            uint      `gorm:"primaryKey" json:"id"`
	AgentName     string    `json:"agentName"`
	AgencyName    string    `json:"agencyName"`
	LoginName     string    `gorm:"unique" json:"loginName"`
	LoginPassword string    `json:"-"`
	AgencyType    string    `json:"agencyType"`
	IsActive      bool      `json:"isActive"`
	CreatedAt     time.Time `json:"createdAt"`
}

type DataRecord struct {
	ID             uint      `gorm:"primaryKey" json:"id"`
	AgentID        uint      `json:"agentId"`
	ClientID       uint      `json:"clientId"`
	ClientCode     string    `json:"clientCode"`
	AgentCode      string    `json:"agentCode"`
	OperationType  string    `json:"operationType"`
	OperationStatus int      `json:"operationStatus"`
	OperationDate  time.Time `json:"operationDate"`
	IPAddress      string    `json:"ipAddress"`
	Agent          Agent     `gorm:"foreignKey:AgentID" json:"agent"`
	Client         Client    `gorm:"foreignKey:ClientID" json:"client"`
}

type LoginAttempt struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	UserID    *uint     `json:"userId"`
	AgentID   *uint     `json:"agentId"`
	IPAddress string    `json:"ipAddress"`
	DeviceID  string    `json:"deviceId"`
	Success   bool      `json:"success"`
	UserType  string    `json:"userType"`
	Timestamp time.Time `json:"timestamp"`
	User      User      `gorm:"foreignKey:UserID" json:"user"`
	Agent     Agent     `gorm:"foreignKey:AgentID" json:"agent"`
}

// ============== تهيئة التطبيق ==============
var db *gorm.DB
const PORT = ":8080"

func main() {
	// 1. تهيئة قاعدة البيانات
	initDB()
	
	// 2. إنشاء خادم Gin
	r := gin.Default()
	
	// 3. إعدادات CORS
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"*"},
		AllowHeaders:     []string{"*"},
		AllowCredentials: true,
	}))

	// 4. تسجيل نقاط النهاية (Endpoints)
	registerRoutes(r)
	
	// 5. بدء الخادم
	log.Printf("🚀 Server running on http://0.0.0.0%s", PORT)
	if err := r.Run(PORT); err != nil {
		log.Fatalf("❌ Failed to start server: %v", err)
	}
}

func initDB() {
	dsn := "host=localhost user=postgres password= dbname= port=5432 sslmode=disable"
	var err error
	db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("❌ Database connection failed: %v", err)
	}

	// تهيئة الجداول
	db.AutoMigrate(
		&User{},
		&Client{},
		&Agent{},
		&DataRecord{},
		&LoginAttempt{},
	)

	log.Println("✅ Database connected")
}

func registerRoutes(r *gin.Engine) {
	// === نقاط نهاية المصادقة ===
	r.POST("/api/auth/login", loginHandler)
	r.POST("/api/client/login", clientLoginHandler)
	r.PUT("/api/client/update", updateClientHandler)
	r.GET("/fix-permissions", fixPermissionsHandler)
	
	// === إدارة المستخدمين ===
	r.GET("/api/users", getUsersHandler)
	r.POST("/api/users", createUserHandler)
	r.PUT("/api/users/:id", updateUserHandler)
	r.PUT("/api/users/:id/password", updateUserPasswordHandler)
	r.PUT("/api/users/:id/device", updateUserDeviceHandler)
	r.DELETE("/api/users/:id", deleteUserHandler)
	
	// === إدارة العملاء ===
	r.GET("/api/clients", getClientsHandler)
	r.POST("/api/clients", createClientHandler)
	r.PUT("/api/clients/:id", updateClientHandler)
	r.DELETE("/api/clients/:id", deleteClientHandler)
	
	// === لوحة التحكم والإحصائيات ===
	r.GET("/api/dashboard/stats", dashboardStatsHandler)
	r.GET("/api/dashboard/recent-activity", recentActivityHandler)
	r.GET("/api/security/stats", securityStatsHandler)
	r.GET("/api/security/login-attempts", loginAttemptsHandler)
	
	// === APIs خارجية ===
	r.POST("/api/external/verify-direct", externalVerifyHandler)
	r.GET("/api/external/stats", externalStatsHandler)
	r.GET("/api/external/health", externalHealthHandler)
	
	// === ملفات ثابتة ===
	r.Static("/static", "./client/dist")
	r.NoRoute(func(c *gin.Context) {
		if strings.HasPrefix(c.Request.URL.Path, "/api/") {
			c.JSON(404, gin.H{"error": "API not found"})
			return
		}
		c.File("./client/dist/index.html")
	})
}

// ============== معالجات المصادقة ==============
func loginHandler(c *gin.Context) {
	type LoginRequest struct {
		LoginName string `json:"loginName"`
		Password  string `json:"password"`
		DeviceID  string `json:"deviceId"`
	}

	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// البحث عن المستخدم
	var user User
	result := db.Where("(login_name = ? OR username = ?) AND is_active = ?", 
		req.LoginName, req.LoginName, true).First(&user)
	
	if result.Error != nil {
		// التحقق من وجود مستخدم غير نشط
		var inactiveUser User
		db.Where("login_name = ? OR username = ?", req.LoginName, req.LoginName).First(&inactiveUser)
		
		if inactiveUser.ID != 0 {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "المستخدم غير نشط"})
			return
		}
		
		c.JSON(http.StatusUnauthorized, gin.H{"error": "المستخدم غير موجود"})
		return
	}

	// التحقق من كلمة المرور
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "كلمة المرور خاطئة"})
		return
	}

	// التحقق من الجهاز
	if (user.DeviceID != "" && user.DeviceID != req.DeviceID) || 
	   (user.Device1 != "" && user.Device1 != req.DeviceID) {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "لا يمكن الوصول من الجهاز الحالي"})
		return
	}

	// إنشاء توكن
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"sub": user.ID,
		"exp": time.Now().Add(24 * time.Hour).Unix(),
	})
	tokenString, _ := token.SignedString([]byte(os.Getenv("JWT_SECRET")))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "تم تسجيل الدخول بنجاح",
		"user": gin.H{
			"id":        user.ID,
			"username":  user.Username,
			"loginName": user.LoginName,
			"permissions": user.Permissions,
			"isActive":  user.IsActive,
		},
		"token": tokenString,
	})
}

func clientLoginHandler(c *gin.Context) {
	// ... (مشابه لـ loginHandler مع تعديلات للعملاء)
}

// ============== معالجات إدارة المستخدمين ==============
func getUsersHandler(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	offset := (page - 1) * limit
	var users []User
	query := db.Offset(offset).Limit(limit).Order("created_at DESC")

	if search != "" {
		query = query.Where("username ILIKE ? OR login_name ILIKE ?", 
			"%"+search+"%", "%"+search+"%")
	}

	var total int64
	db.Model(&User{}).Count(&total)

	query.Find(&users)

	c.JSON(http.StatusOK, gin.H{
		"data":       users,
		"total":      total,
		"page":       page,
		"totalPages": (int(total) + limit - 1) / limit,
	})
}

func createUserHandler(c *gin.Context) {
	var newUser struct {
		Username   string      `json:"username"`
		LoginName  string      `json:"loginName"`
		Password   string      `json:"password"`
		DeviceID   string      `json:"deviceId"`
		Device1    string      `json:"device1"`
		Permissions Permissions `json:"permissions"`
		IsActive   bool        `json:"isActive"`
	}

	if err := c.ShouldBindJSON(&newUser); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(newUser.Password), bcrypt.DefaultCost)
	user := User{
		Username:   newUser.Username,
		LoginName:  newUser.LoginName,
		Password:   string(hashedPassword),
		DeviceID:   newUser.DeviceID,
		Device1:    newUser.Device1,
		Permissions: newUser.Permissions,
		IsActive:   newUser.IsActive,
	}

	if result := db.Create(&user); result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "فشل في إنشاء المستخدم"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// ============== معالجات إدارة العملاء ==============
func createClientHandler(c *gin.Context) {
	var newClient struct {
		ClientName string `json:"clientName"`
		AppName    string `json:"appName"`
		CardNumber string `json:"cardNumber"`
		Password   string `json:"password"`
		IPAddress  string `json:"ipAddress"`
		Status     int    `json:"status"`
		UserID     uint   `json:"userId"`
	}

	if err := c.ShouldBindJSON(&newClient); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// توليد كود العميل
	var lastClient Client
	db.Order("client_code DESC").First(&lastClient)
	clientCode := lastClient.ClientCode + 1

	// توليد التوكن
	token := generateToken(12)

	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(newClient.Password), bcrypt.DefaultCost)
	client := Client{
		ClientCode: clientCode,
		ClientName: newClient.ClientName,
		AppName:    newClient.AppName,
		CardNumber: newClient.CardNumber,
		Password:   string(hashedPassword),
		Token:      token,
		Status:     newClient.Status,
		IPAddress:  newClient.IPAddress,
		UserID:     &newClient.UserID,
	}

	if result := db.Create(&client); result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "فشل في إنشاء العميل"})
		return
	}

	c.JSON(http.StatusOK, client)
}

func generateToken(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// ============== معالجات لوحة التحكم ==============
func dashboardStatsHandler(c *gin.Context) {
	var stats struct {
		TotalUsers      int64 `json:"totalUsers"`
		TotalClients    int64 `json:"totalClients"`
		TotalAgents     int64 `json:"totalAgents"`
		TotalDataRecords int64 `json:"totalDataRecords"`
		TotalSecurity   int64 `json:"totalSecurity"`
	}

	db.Model(&User{}).Count(&stats.TotalUsers)
	db.Model(&Client{}).Count(&stats.TotalClients)
	db.Model(&Agent{}).Count(&stats.TotalAgents)
	db.Model(&DataRecord{}).Count(&stats.TotalDataRecords)
	db.Model(&LoginAttempt{}).Count(&stats.TotalSecurity)

	stats.TotalSecurity = 0 // مخصص للإحصائيات الأمنية

	c.JSON(http.StatusOK, stats)
}

// ============== معالجات APIs خارجية ==============
func externalVerifyHandler(c *gin.Context) {
	// ... (تنفيذ منطق التحقق المباشر)
}

// ============== وظائف مساعدة ==============
func fixPermissionsHandler(c *gin.Context) {
	adminPermissions := Permissions{
		IsAdmin: true,
		// ... (تعيين جميع الصلاحيات إلى true)
	}

	result := db.Model(&User{}).
		Where("login_name = ?", "hash8080").
		Updates(map[string]interface{}{
			"permissions": adminPermissions,
			"is_active":   true,
		})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "فشل في إصلاح الصلاحيات"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "تم إصلاح صلاحيات hash8080 بنجاح",
		"permissions": adminPermissions,
	})
}