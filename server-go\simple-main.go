package main

import (
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// Response structures
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

type HealthResponse struct {
	Status    string                 `json:"status"`
	Timestamp string                 `json:"timestamp"`
	Database  map[string]interface{} `json:"database"`
	Server    map[string]interface{} `json:"server"`
}

// Setup routes
func setupRoutes() *gin.Engine {
	// Set Gin mode
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()

	// Middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS configuration (مطابق لـ working-server.js)
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowCredentials = true
	config.AllowHeaders = []string{"*"}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"}
	router.Use(cors.New(config))

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		response := HealthResponse{
			Status:    "OK",
			Timestamp: time.Now().Format(time.RFC3339),
			Database: map[string]interface{}{
				"status":       "simulated",
				"responseTime": "1ms",
				"users":        10,
				"clients":      25,
				"agents":       15,
			},
			Server: map[string]interface{}{
				"language":  "Go",
				"framework": "Gin",
				"version":   "1.0.0",
			},
		}
		c.JSON(http.StatusOK, response)
	})

	// API test endpoint
	router.GET("/api/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, APIResponse{
			Success: true,
			Message: "API يعمل بشكل صحيح - Golang",
			Data: map[string]interface{}{
				"timestamp": time.Now().Format(time.RFC3339),
				"server":    "Yemen Client Management System - Golang",
				"version":   "1.0.0",
				"status":    "operational",
			},
		})
	})

	// Root endpoint
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "🎉 خادم Golang الكامل يعمل بنجاح!",
			"status":  "ready",
			"version": "complete",
		})
	})

	// Authentication routes (مبسطة)
	router.POST("/api/auth/login", func(c *gin.Context) {
		var loginData struct {
			LoginName string `json:"loginName"`
			Password  string `json:"password"`
			DeviceID  string `json:"deviceId"`
		}

		if err := c.ShouldBindJSON(&loginData); err != nil {
			c.JSON(http.StatusBadRequest, APIResponse{
				Success: false,
				Message: "جميع الحقول مطلوبة",
			})
			return
		}

		// تسجيل دخول مبسط (للاختبار)
		if loginData.LoginName == "hash8080" && loginData.Password == "yemen123456" {
			c.JSON(http.StatusOK, APIResponse{
				Success: true,
				Message: "تم تسجيل الدخول بنجاح",
				Data: map[string]interface{}{
					"user": map[string]interface{}{
						"id":          1,
						"username":    "hash8080",
						"loginName":   "hash8080",
						"permissions": "admin",
					},
					"token": "golang-test-token-123",
				},
			})
		} else {
			c.JSON(http.StatusUnauthorized, APIResponse{
				Success: false,
				Message: "اسم المستخدم أو كلمة المرور غير صحيحة",
			})
		}
	})

	// Dashboard APIs (مبسطة)
	router.GET("/api/dashboard/stats", func(c *gin.Context) {
		c.JSON(http.StatusOK, APIResponse{
			Success: true,
			Data: map[string]interface{}{
				"totalUsers":       10,
				"totalClients":     25,
				"activeClients":    20,
				"blockedClients":   5,
				"totalAgents":      15,
				"activeAgents":     12,
				"inactiveAgents":   3,
				"totalDataRecords": 150,
				"timestamp":        time.Now().Format(time.RFC3339),
			},
		})
	})

	// Security APIs (مبسطة)
	router.GET("/api/security/stats", func(c *gin.Context) {
		c.JSON(http.StatusOK, APIResponse{
			Success: true,
			Data: map[string]interface{}{
				"totalLoginAttempts": 100,
				"successfulLogins":   80,
				"failedLogins":       20,
				"blockedIPs":         5,
				"securityEvents":     3,
				"lastSecurityCheck":  time.Now().Format(time.RFC3339),
			},
		})
	})

	// Data APIs (مبسطة)
	router.GET("/api/users", func(c *gin.Context) {
		users := []map[string]interface{}{
			{"id": 1, "username": "hash8080", "loginName": "hash8080", "isActive": true},
			{"id": 2, "username": "admin", "loginName": "admin", "isActive": true},
		}
		c.JSON(http.StatusOK, APIResponse{
			Success: true,
			Data:    users,
		})
	})

	router.GET("/api/clients", func(c *gin.Context) {
		clients := []map[string]interface{}{
			{"id": 1, "clientCode": 1000, "clientName": "عميل تجريبي", "status": 1},
			{"id": 2, "clientCode": 1001, "clientName": "عميل آخر", "status": 1},
		}
		c.JSON(http.StatusOK, APIResponse{
			Success: true,
			Data:    clients,
		})
	})

	router.GET("/api/agents", func(c *gin.Context) {
		agents := []map[string]interface{}{
			{"id": 1, "agentCode": 2000, "agentName": "وكيل تجريبي", "isActive": true},
			{"id": 2, "agentCode": 2001, "agentName": "وكيل آخر", "isActive": true},
		}
		c.JSON(http.StatusOK, APIResponse{
			Success: true,
			Data:    agents,
		})
	})

	// External APIs (مبسطة)
	router.GET("/api/external/stats", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"total_clients":    25,
				"active_clients":   20,
				"blocked_clients":  5,
				"total_agents":     15,
				"active_agents":    12,
				"inactive_agents":  3,
				"total_operations": 150,
				"server_status":    "operational",
				"last_updated":     time.Now().Format(time.RFC3339),
			},
		})
	})

	router.GET("/api/external/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"success":   true,
			"status":    "healthy",
			"database":  "simulated",
			"server":    "operational",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})

	// Static files (React build)
	router.Static("/static", "../client/dist/assets")
	router.StaticFile("/favicon.ico", "../client/dist/favicon.ico")

	// Catch all routes for React Router
	router.NoRoute(func(c *gin.Context) {
		if len(c.Request.URL.Path) >= 4 && c.Request.URL.Path[:4] == "/api" {
			c.JSON(http.StatusNotFound, APIResponse{
				Success: false,
				Error:   "API not found",
			})
			return
		}
		c.File("../client/dist/index.html")
	})

	return router
}

func main() {
	log.Println("🚀 ========================================")
	log.Println("🏢 Yemen Client Management System - Golang Complete")
	log.Println("🚀 ========================================")

	// Setup routes
	router := setupRoutes()

	// Get port from environment
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("📡 Server starting on port: %s", port)
	log.Printf("🌐 Local access: http://localhost:%s", port)
	log.Printf("🌐 External access: http://***********:%s", port)
	log.Println("✅ Server ready for connections!")
	log.Println("🚀 ========================================")

	// Start server on all interfaces (0.0.0.0)
	if err := router.Run("0.0.0.0:" + port); err != nil {
		log.Fatal("❌ Failed to start server:", err)
	}
}
