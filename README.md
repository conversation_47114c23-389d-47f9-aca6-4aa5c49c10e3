# 🔐 نظام إدارة العملاء والوكلاء - محمي بالكامل

نظام شامل لإدارة العملاء والوكلاء مع واجهة عربية احترافية وحماية أمنية متقدمة.

## المتطلبات

- Node.js (الإصدار 16 أو أحدث)
- PostgreSQL (الإصدار 12 أو أحدث)
- npm أو yarn

## التثبيت والتشغيل

### 1. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
createdb yemclient_db

# تشغيل سكريبت إنشاء الجداول
psql -U postgres -d yemclient_db -f create_database.sql
```

### 2. إعداد الخادم
```bash
cd server
npm install
npm start
```

### 3. إعد<PERSON> العميل
```bash
cd client
npm install
npm run build
```

### 4. تشغيل النظام الآمن
```bash
# تشغيل سريع (Windows)
start-yemen-server.bat

# أو تشغيل يدوي
cd server && npm start
```

## 🔐 الحماية الأمنية

### ✅ النظام محمي بالكامل:
- **JWT Authentication**: جميع APIs تتطلب توكن صحيح
- **Device ID Binding**: ربط الجلسات بأجهزة محددة
- **Rate Limiting**: حماية من الهجمات المكثفة
- **Permission System**: نظام صلاحيات متقدم
- **Data Encryption**: تشفير البيانات الحساسة
- **SQL Injection Prevention**: حماية من حقن SQL
- **XSS Prevention**: حماية من هجمات XSS

### 🚫 لا يمكن الوصول للبيانات بدون مصادقة:
```bash
# هذه الروابط لا تعمل بعد الآن بدون توكن:
❌ http://***********:8080/api/clients
❌ http://***********:8080/api/agents
❌ http://***********:8080/api/users
```

## الوصول للنظام

- **المنفذ المحلي**: http://localhost:8080
- **المنفذ الخارجي**: http://***********:8080
- **تسجيل الدخول مطلوب**: جميع الصفحات تتطلب مصادقة

## اختبار الحماية

```bash
# تشغيل اختبار الحماية الأمنية
node test-security.js
```

## المزايا

- ✅ واجهة عربية احترافية
- ✅ إدارة العملاء والوكلاء
- ✅ نظام مستخدمين متقدم
- ✅ سجلات البيانات التلقائية
- ✅ نظام أمان شامل
- ✅ تصميم متجاوب

## الملفات الأساسية

### الخادم (server/)
- `server.js` - الخادم الرئيسي
- `package.json` - تبعيات الخادم
- `prisma/` - إعدادات قاعدة البيانات

### العميل (client/)
- `src/` - كود المصدر
- `public/` - الملفات العامة
- `package.json` - تبعيات العميل

### قاعدة البيانات
- `create_database.sql` - سكريبت إنشاء الجداول
- `init.sql` - البيانات الأولية

### التشغيل
- `start_system.bat` - تشغيل النظام
- `package.json` - إعدادات المشروع الرئيسية

## الدعم

للدعم الفني، يرجى مراجعة الملفات المرفقة أو التواصل مع فريق التطوير.
