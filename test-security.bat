@echo off
chcp 65001 >nul
echo ========================================
echo      🔐 اختبار الحماية الأمنية
echo ========================================
echo.

echo 📋 التحقق من متطلبات الاختبار...

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js أولاً
    pause
    exit /b 1
)

echo ✅ Node.js متوفر

REM التحقق من وجود ملف الاختبار
if not exist "test-security.js" (
    echo ❌ ملف الاختبار غير موجود: test-security.js
    pause
    exit /b 1
)

echo ✅ ملف الاختبار موجود

REM التحقق من تشغيل الخادم
echo.
echo 🔍 التحقق من تشغيل الخادم...
curl -s http://localhost:8080/health >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  الخادم لا يعمل على المنفذ 8080
    echo.
    echo هل تريد تشغيل الخادم أولاً؟ (y/n)
    set /p choice="اختر: "
    if /i "%choice%"=="y" (
        echo.
        echo 🚀 تشغيل الخادم...
        start "Yemen Server" cmd /k "cd server && npm start"
        echo انتظار تشغيل الخادم...
        timeout /t 10 /nobreak >nul
    ) else (
        echo يرجى تشغيل الخادم أولاً ثم إعادة تشغيل الاختبار
        pause
        exit /b 1
    )
)

echo ✅ الخادم يعمل

echo.
echo 🧪 بدء اختبار الحماية الأمنية...
echo =====================================
echo.

REM تشغيل اختبار الحماية
node test-security.js

echo.
echo ========================================
echo        انتهى اختبار الحماية
echo ========================================
echo.

REM عرض تقرير الحماية إذا كان موجوداً
if exist "SECURITY_REPORT.md" (
    echo 📄 لعرض تقرير الحماية الكامل، افتح الملف:
    echo    SECURITY_REPORT.md
    echo.
)

echo اضغط أي مفتاح للخروج...
pause >nul
