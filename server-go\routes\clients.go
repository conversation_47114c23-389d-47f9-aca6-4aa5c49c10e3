package routes

import (
	"crypto/rand"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// generateToken - توليد توكن عشوائي مطابق لـ clients.js
func generateToken() string {
	const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
	token := make([]byte, 12)
	for i := range token {
		randomByte := make([]byte, 1)
		rand.Read(randomByte)
		token[i] = chars[randomByte[0]%byte(len(chars))]
	}
	return string(token)
}

// ClientsRoutes - مطابق تماماً لـ server/routes/clients.js
func ClientsRoutes(router *gin.RouterGroup, db *gorm.DB) {
	// الحصول على جميع العملاء - مطابق لـ clients.js
	router.GET("/", func(c *gin.Context) {
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
		search := c.DefaultQuery("search", "")
		status := c.DefaultQuery("status", "")
		offset := (page - 1) * limit

		if db == nil {
			// Mock data when no database
			c.JSON(http.StatusOK, gin.H{
				"success":    true,
				"data":       []gin.H{},
				"total":      0,
				"page":       page,
				"totalPages": 0,
			})
			return
		}

		// Build where clause
		query := db.Table("clients")
		
		if search != "" {
			searchInt, _ := strconv.Atoi(search)
			query = query.Where("client_name ILIKE ? OR app_name ILIKE ? OR card_number ILIKE ? OR client_code = ?", 
				"%"+search+"%", "%"+search+"%", "%"+search+"%", searchInt)
		}

		if status != "" {
			statusInt, _ := strconv.Atoi(status)
			query = query.Where("status = ?", statusInt)
		}

		// Get total count
		var total int64
		query.Count(&total)

		// Get clients with pagination
		var clients []map[string]interface{}
		query.Select("id, client_code, client_name, app_name, card_number, ip_address, status, user_id, created_at, updated_at").
			Offset(offset).Limit(limit).
			Order("created_at DESC").
			Find(&clients)

		// Add user information for each client
		for i := range clients {
			if userID, ok := clients[i]["user_id"]; ok && userID != nil {
				var user struct {
					ID       uint   `json:"id"`
					Username string `json:"username"`
					LoginName string `json:"loginName"`
				}
				db.Table("users").Select("id, username, login_name").Where("id = ?", userID).First(&user)
				clients[i]["user"] = user
			}
		}

		totalPages := int((total + int64(limit) - 1) / int64(limit))

		c.JSON(http.StatusOK, gin.H{
			"success":    true,
			"data":       clients,
			"total":      total,
			"page":       page,
			"totalPages": totalPages,
		})
	})

	// إضافة عميل جديد - مطابق لـ clients.js
	router.POST("/", func(c *gin.Context) {
		var clientData struct {
			ClientName string  `json:"clientName" binding:"required"`
			AppName    *string `json:"appName"`
			CardNumber *string `json:"cardNumber"`
			Password   string  `json:"password" binding:"required"`
			IPAddress  *string `json:"ipAddress"`
			Status     *int    `json:"status"`
			UserID     *uint   `json:"userId"`
		}

		if err := c.ShouldBindJSON(&clientData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		log.Printf("➕ Creating new client: %s, %v, %v", clientData.ClientName, clientData.AppName, clientData.UserID)

		if db == nil {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "تم إنشاء العميل بنجاح (mock)",
				"client": gin.H{
					"id":         1,
					"clientName": clientData.ClientName,
					"clientCode": 1001,
				},
			})
			return
		}

		// تشفير كلمة المرور
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(clientData.Password), 10)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "خطأ في تشفير كلمة المرور",
			})
			return
		}

		// توليد رمز العميل تلقائياً
		var lastClient struct {
			ClientCode int `json:"clientCode"`
		}
		db.Table("clients").Select("client_code").Order("client_code DESC").First(&lastClient)
		nextClientCode := lastClient.ClientCode + 1
		if nextClientCode == 1 {
			nextClientCode = 1001 // البداية من 1001
		}

		// توليد التوكن المشفر
		clientToken := generateToken()

		// تحديد الحالة الافتراضية
		status := 1
		if clientData.Status != nil {
			status = *clientData.Status
		}

		// إنشاء العميل
		newClient := map[string]interface{}{
			"client_name":  clientData.ClientName,
			"app_name":     clientData.AppName,
			"card_number":  clientData.CardNumber,
			"password":     string(hashedPassword),
			"ip_address":   clientData.IPAddress,
			"status":       status,
			"client_code":  nextClientCode,
			"token":        clientToken,
			"user_id":      clientData.UserID,
			"created_at":   time.Now(),
			"updated_at":   time.Now(),
		}

		result := db.Table("clients").Create(&newClient)
		if result.Error != nil {
			log.Printf("❌ Create client error: %v", result.Error)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "خطأ في إنشاء العميل",
			})
			return
		}

		// إضافة معلومات المستخدم إذا كان موجوداً
		var user struct {
			ID       uint   `json:"id"`
			Username string `json:"username"`
			LoginName string `json:"loginName"`
		}
		if clientData.UserID != nil {
			db.Table("users").Select("id, username, login_name").Where("id = ?", *clientData.UserID).First(&user)
		}

		log.Printf("✅ Client created successfully with ID: %v, userId: %v", newClient["id"], clientData.UserID)

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "تم إنشاء العميل بنجاح",
			"client": gin.H{
				"id":         newClient["id"],
				"clientCode": nextClientCode,
				"clientName": clientData.ClientName,
				"appName":    clientData.AppName,
				"cardNumber": clientData.CardNumber,
				"ipAddress":  clientData.IPAddress,
				"status":     status,
				"token":      clientToken,
				"userId":     clientData.UserID,
				"user":       user,
				"createdAt":  newClient["created_at"],
			},
		})
	})

	// تحديث عميل - مطابق لـ clients.js
	router.PUT("/:id", func(c *gin.Context) {
		clientID, err := strconv.Atoi(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "معرف العميل غير صحيح",
			})
			return
		}

		var updateData map[string]interface{}
		if err := c.ShouldBindJSON(&updateData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		log.Printf("🔄 Updating client %d: %v", clientID, updateData)

		if db == nil {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "تم تحديث العميل بنجاح (mock)",
			})
			return
		}

		// تحضير بيانات التحديث
		updateFields := map[string]interface{}{
			"updated_at": time.Now(),
		}

		if clientName, ok := updateData["clientName"]; ok {
			updateFields["client_name"] = clientName
		}
		if appName, ok := updateData["appName"]; ok {
			updateFields["app_name"] = appName
		}
		if cardNumber, ok := updateData["cardNumber"]; ok {
			updateFields["card_number"] = cardNumber
		}
		if ipAddress, ok := updateData["ipAddress"]; ok {
			updateFields["ip_address"] = ipAddress
		}
		if status, ok := updateData["status"]; ok {
			updateFields["status"] = status
		}

		// تشفير كلمة المرور إذا تم تغييرها
		if password, ok := updateData["password"]; ok && password != "" {
			hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password.(string)), 10)
			if err == nil {
				updateFields["password"] = string(hashedPassword)
			}
		}

		// تحديث العميل
		result := db.Table("clients").Where("id = ?", clientID).Updates(updateFields)
		if result.Error != nil {
			log.Printf("❌ Update client error: %v", result.Error)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "خطأ في تحديث العميل",
			})
			return
		}

		if result.RowsAffected == 0 {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "العميل غير موجود",
			})
			return
		}

		log.Printf("✅ Client updated successfully: ID %d", clientID)

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "تم تحديث العميل بنجاح",
		})
	})

	// حذف عميل - مطابق لـ clients.js
	router.DELETE("/:id", func(c *gin.Context) {
		clientID, err := strconv.Atoi(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "معرف العميل غير صحيح",
			})
			return
		}

		if db == nil {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "تم حذف العميل بنجاح (mock)",
			})
			return
		}

		// حذف العميل
		result := db.Table("clients").Where("id = ?", clientID).Delete(nil)
		if result.Error != nil {
			log.Printf("❌ Delete client error: %v", result.Error)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "خطأ في حذف العميل",
			})
			return
		}

		if result.RowsAffected == 0 {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "العميل غير موجود",
			})
			return
		}

		log.Printf("✅ Client deleted successfully: ID %d", clientID)

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "تم حذف العميل بنجاح",
		})
	})

	// تسجيل دخول العميل - مطابق لـ clients.js
	router.POST("/login", func(c *gin.Context) {
		var loginData struct {
			ClientCode string `json:"clientCode" binding:"required"`
			Password   string `json:"password" binding:"required"`
		}

		if err := c.ShouldBindJSON(&loginData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "رمز العميل وكلمة المرور مطلوبان",
			})
			return
		}

		log.Printf("🏢 Client Login: %s, passwordLength: %d", loginData.ClientCode, len(loginData.Password))

		if db == nil {
			// Mock client authentication
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "تم تسجيل الدخول بنجاح",
				"client": gin.H{
					"id":         1,
					"clientCode": loginData.ClientCode,
					"clientName": "عميل تجريبي",
					"status":     1,
				},
			})
			return
		}

		log.Printf("🔍 Searching for client with code: \"%s\"", loginData.ClientCode)

		clientCode, err := strconv.Atoi(loginData.ClientCode)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "رمز العميل غير صحيح",
			})
			return
		}

		var client struct {
			ID         uint    `json:"id"`
			ClientCode int     `json:"clientCode"`
			ClientName string  `json:"clientName"`
			Password   string  `json:"password"`
			Token      *string `json:"token"`
			AppName    *string `json:"appName"`
			IPAddress  *string `json:"ipAddress"`
			Status     int     `json:"status"`
		}

		err = db.Table("clients").Where("client_code = ?", clientCode).First(&client).Error
		if err != nil {
			log.Printf("❌ Client not found: %s", loginData.ClientCode)
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "رمز العميل غير صحيح",
			})
			return
		}

		// التحقق من حالة العميل
		if client.Status != 1 {
			log.Printf("❌ Client is not active: %s, status: %d", loginData.ClientCode, client.Status)
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "حساب العميل غير نشط",
			})
			return
		}

		log.Printf("✅ Client found and active: %s", client.ClientName)

		// التحقق من كلمة المرور
		err = bcrypt.CompareHashAndPassword([]byte(client.Password), []byte(loginData.Password))
		if err != nil {
			log.Printf("❌ Invalid password for client: %s", loginData.ClientCode)
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "كلمة المرور غير صحيحة",
			})
			return
		}

		log.Printf("✅ Password valid for client: %s", client.ClientName)

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "تم تسجيل الدخول بنجاح",
			"client": gin.H{
				"id":         client.ID,
				"clientCode": client.ClientCode,
				"clientName": client.ClientName,
				"token":      client.Token,
				"appName":    client.AppName,
				"ipAddress":  client.IPAddress,
				"status":     client.Status,
			},
		})
	})
}
