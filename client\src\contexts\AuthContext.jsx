import React, { createContext, useContext, useState, useEffect } from 'react'

// إنشاء Context لحالة المصادقة
const AuthContext = createContext({})

console.log('🎯 النظام يعمل مع قاعدة البيانات الحقيقية')

// دالة لتوليد رقم جهاز فريد
const generateDeviceId = () => {
  const stored = localStorage.getItem('deviceId')
  if (stored) return stored

  const deviceId = `device_${Math.random().toString(36).substring(2, 11)}_${Date.now()}`
  localStorage.setItem('deviceId', deviceId)
  return deviceId
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const token = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    const clientData = localStorage.getItem('clientData')

    // إذا كان هناك مستخدم عادي
    if (token && savedUser) {
      try {
        setUser(JSON.parse(savedUser))
      } catch (error) {
        console.error('Error parsing saved user:', error)
        localStorage.removeItem('token')
        localStorage.removeItem('user')
      }
    }
    // إذا كان هناك عميل مسجل دخول
    else if (clientData) {
      try {
        const client = JSON.parse(clientData)
        // إنشاء كائن مستخدم وهمي للعميل
        setUser({
          id: client.id,
          username: client.clientName,
          loginName: client.clientCode.toString(),
          userType: 'client',
          isClient: true,
          clientData: client
        })
      } catch (error) {
        console.error('Error parsing client data:', error)
        localStorage.removeItem('clientData')
      }
    }

    setLoading(false)
  }, [])

  const login = async (loginName, password, deviceId) => {
    console.log('🚀 تسجيل دخول عبر الخادم - من قاعدة البيانات')
    console.log('🚀 Parameters received:', { loginName, password: password ? '***' : 'undefined', deviceId })

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          loginName,
          password,
          deviceId
        })
      })

      const result = await response.json()

      console.log('🔍 Response status:', response.status)
      console.log('🔍 Response ok:', response.ok)
      console.log('🔍 Response data:', result)

      // تبسيط المنطق - إذا كان الخادم يرد بـ 200 فهذا يعني نجاح
      if (response.ok) {
        // حفظ البيانات محلياً
        if (result.token) {
          localStorage.setItem('token', result.token)
        }
        if (result.user) {
          localStorage.setItem('user', JSON.stringify(result.user))
          setUser(result.user)
        }

        console.log('✅ تم تسجيل الدخول بنجاح (HTTP 200)')
        return { success: true }
      } else {
        console.log('❌ فشل تسجيل الدخول - HTTP Status:', response.status)
        return {
          success: false,
          error: result.error || `خطأ HTTP ${response.status}`,
          details: result.details
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        error: 'حدث خطأ في الاتصال بالخادم'
      }
    }
  }

  const clientLogin = async (clientCode, password) => {
    console.log('🏢 تسجيل دخول العميل عبر الخادم - من قاعدة البيانات')
    console.log('🏢 Parameters received:', { clientCode, password: password ? '***' : 'undefined' })

    try {
      const response = await fetch('/api/client/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientCode: parseInt(clientCode),
          password
        })
      })

      const result = await response.json()

      console.log('🔍 Client Response status:', response.status)
      console.log('🔍 Client Response ok:', response.ok)
      console.log('🔍 Client Response data:', result)

      if (response.ok && result.success) {
        // حفظ بيانات العميل محلياً
        const clientData = {
          id: result.client.id,
          clientCode: result.client.clientCode,
          clientName: result.client.clientName,
          token: result.client.token,
          appName: result.client.appName,
          ipAddress: result.client.ipAddress,
          loginTime: new Date().toISOString()
        }

        localStorage.setItem('clientData', JSON.stringify(clientData))

        // تحديث حالة المستخدم في AuthContext
        setUser({
          id: result.client.id,
          username: result.client.clientName,
          loginName: result.client.clientCode.toString(),
          userType: 'client',
          isClient: true,
          clientData: clientData
        })

        console.log('✅ تم تسجيل دخول العميل بنجاح (HTTP 200)')
        return { success: true, client: result.client }
      } else {
        console.log('❌ فشل تسجيل دخول العميل - HTTP Status:', response.status)
        return {
          success: false,
          error: result.message || `خطأ HTTP ${response.status}`
        }
      }
    } catch (error) {
      console.error('Client login error:', error)
      return {
        success: false,
        error: 'حدث خطأ في الاتصال بالخادم'
      }
    }
  }

  const logout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('clientData')
    setUser(null)
  }

  // تم إزالة دالة approveDevice - لا حاجة لها في الوضع المحلي

  const hasPermission = (resource, action) => {
    // التحقق من وجود المستخدم
    if (!user || !user.permissions) return false

    // الأدمن له كل الصلاحيات
    if (user.permissions.isAdmin) return true

    // التحقق من الصلاحية المحددة
    return user.permissions[resource]?.[action] || false
  }

  // إنشاء كائن API مع التوكن والـ device ID
  const api = {
    get: async (url, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      // معالجة query parameters
      let finalUrl = url
      if (config.params) {
        const searchParams = new URLSearchParams()
        Object.keys(config.params).forEach(key => {
          if (config.params[key] !== undefined && config.params[key] !== null && config.params[key] !== '') {
            searchParams.append(key, config.params[key])
          }
        })
        const queryString = searchParams.toString()
        if (queryString) {
          finalUrl += (url.includes('?') ? '&' : '?') + queryString
        }
      }

      const response = await fetch(finalUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        }
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          // إذا كان التوكن غير صالح، قم بتسجيل الخروج
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    },

    post: async (url, data, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    },

    put: async (url, data, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    },

    delete: async (url, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        }
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    }
  }

  const value = {
    user,
    loading,
    login,
    clientLogin,
    logout,
    hasPermission,
    api
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// خطأ useAuth إذا لم توجد
export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
