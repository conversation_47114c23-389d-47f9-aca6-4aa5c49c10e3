﻿const express = require('express');
const cors = require('cors');
const path = require('path');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const app = express();
const PORT = 8080;
const prisma = new PrismaClient();

console.log('🚀 Starting Working Server...');

// Middleware
app.use(cors({ origin: '*', credentials: true }));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Test database connection
prisma.$connect().then(() => {
  console.log('✅ Database connected');
  prisma.user.count().then(count => console.log(`👥 Users: ${count}`));
  prisma.client.count().then(count => console.log(`🏢 Clients: ${count}`));
  prisma.agent.count().then(count => console.log(`🤝 Agents: ${count}`));
}).catch(err => console.error('❌ Database error:', err));

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// إصلاح صلاحيات hash8080 (مؤقت)
app.get('/fix-permissions', async (req, res) => {
  try {
    const adminPermissions = {
      isAdmin: true,
      users: { read: true, create: true, update: true, delete: true },
      clients: { read: true, create: true, update: true, delete: true },
      agents: { read: true, create: true, update: true, delete: true },
      dataRecords: { read: true, create: true, update: true, delete: true },
      security: { read: true, create: true, update: true, delete: true },
      dashboard: { read: true }
    };

    const updatedUser = await prisma.user.update({
      where: { loginName: 'hash8080' },
      data: {
        permissions: adminPermissions,
        isActive: true
      }
    });

    console.log('✅ Fixed permissions for hash8080:', adminPermissions);

    res.json({
      success: true,
      message: 'تم إصلاح صلاحيات hash8080 بنجاح',
      permissions: adminPermissions
    });
  } catch (error) {
    console.error('Fix permissions error:', error);
    res.status(500).json({ error: 'فشل في إصلاح الصلاحيات' });
  }
});

// Authentication
app.post('/api/auth/login', async (req, res) => {
  const { loginName, password, deviceId } = req.body;
  console.log('🔐 Login:', { loginName, deviceId });

  try {
    console.log(`🔍 Searching for user with loginName: "${loginName}"`);

    const user = await prisma.user.findFirst({
      where: {
        OR: [{ loginName }, { username: loginName }],
        isActive: true
      },
      select: {
        id: true,
        username: true,
        loginName: true,
        password: true,
        deviceId: true,
        device1: true,
        permissions: true,
        isActive: true
      }
    });

    if (!user) {
      console.log(`❌ User not found: "${loginName}"`);

      // البحث بدون شرط isActive للتشخيص
      const inactiveUser = await prisma.user.findFirst({
        where: {
          OR: [{ loginName }, { username: loginName }]
        },
        select: {
          id: true,
          username: true,
          loginName: true,
          deviceId: true,
          device1: true,
          isActive: true
        }
      });

      if (inactiveUser) {
        console.log(`⚠️ User found but inactive: ${inactiveUser.username}, active: ${inactiveUser.isActive}`);
        return res.status(401).json({ success: false, error: 'المستخدم غير نشط' });
      }

      return res.status(401).json({ success: false, error: 'المستخدم غير موجود' });
    }

    console.log(`✅ User found: ${user.username} (ID: ${user.id})`);

    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) {
      return res.status(401).json({ success: false, error: 'كلمة المرور خاطئة' });
    }

    // التحقق من الجهاز - يدعم deviceId و device1
    console.log(`🔍 Device check: user.deviceId="${user.deviceId}", user.device1="${user.device1}", provided="${deviceId}"`);

    // التحقق من أن الجهاز مطابق لأحد العمودين
    const isDeviceAuthorized = (
      (user.deviceId && user.deviceId === deviceId) ||
      (user.device1 && user.device1 === deviceId)
    );

    // إذا كان هناك أجهزة محفوظة ولم يطابق أي منها
    if ((user.deviceId || user.device1) && !isDeviceAuthorized) {
      console.log(`❌ Device not authorized for user ${user.username}`);
      return res.status(401).json({
        success: false,
        error: 'لا يمكن الوصول من الجهاز الحالي'
      });
    }

    console.log(`✅ Device check passed for user ${user.username}`);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions,
       isActive: user.isActive  // ← المضاف
      },
      token: `token_${user.id}_${Date.now()}`
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, error: 'خطأ في الخادم' });
  }
});

// Client login API
app.post('/api/client/login', async (req, res) => {
  const { clientCode, password } = req.body;
  console.log('🏢 Client Login:', { clientCode, passwordLength: password?.length });

  try {
    console.log(`🔍 Searching for client with code: "${clientCode}"`);

    // أولاً، دعنا نتحقق من وجود العميل بدون شرط isActive
    const allClients = await prisma.client.findMany({
      select: {
        id: true,
        clientCode: true,
        clientName: true,
        status: true
      }
    });
    console.log('📋 All clients in database:', allClients);

    const client = await prisma.client.findFirst({
      where: {
        clientCode: parseInt(clientCode)
      },
      select: {
        id: true,
        clientCode: true,
        clientName: true,
        password: true,
        token: true,
        status: true,
        appName: true,
        ipAddress: true
      }
    });

    console.log('🔍 Found client:', client ? { id: client.id, clientCode: client.clientCode, clientName: client.clientName, status: client.status } : 'Not found');

    if (!client) {
      console.log(`❌ Client not found: ${clientCode}`);
      return res.status(401).json({
        success: false,
        message: 'رمز العميل غير صحيح'
      });
    }

    // التحقق من حالة العميل (status = 1 يعني نشط)
    if (client.status !== 1) {
      console.log(`❌ Client is not active: ${clientCode}, status: ${client.status}`);
      return res.status(401).json({
        success: false,
        message: 'حساب العميل غير نشط'
      });
    }

    console.log(`✅ Client found and active: ${client.clientName}`);

    // التحقق من كلمة المرور
    console.log(`🔐 Checking password for client: ${clientCode}`);
    console.log(`🔐 Password hash in DB: ${client.password?.substring(0, 20)}...`);
    console.log(`🔐 Input password: ${password}`);

    const isPasswordValid = await bcrypt.compare(password, client.password);
    console.log(`🔐 Password validation result: ${isPasswordValid}`);

    if (!isPasswordValid) {
      console.log(`❌ Invalid password for client: ${clientCode}`);
      return res.status(401).json({
        success: false,
        message: 'كلمة المرور غير صحيحة'
      });
    }

    console.log(`✅ Password valid for client: ${client.clientName}`);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      client: {
        id: client.id,
        clientCode: client.clientCode,
        clientName: client.clientName,
        token: client.token,
        appName: client.appName,
        ipAddress: client.ipAddress,
        status: client.status
      }
    });
  } catch (error) {
    console.error('Client login error:', error);
    res.status(500).json({ success: false, message: 'خطأ في الخادم' });
  }
});

// Client update API
app.put('/api/client/update', async (req, res) => {
  const { clientId, password, token } = req.body;
  console.log('🔄 Client Update:', { clientId, hasPassword: !!password, hasToken: !!token });

  try {
    const updateData = {};

    // تحديث كلمة المرور إذا تم توفيرها
    if (password) {
      const hashedPassword = await bcrypt.hash(password, 10);
      updateData.password = hashedPassword;
    }

    // تحديث رمز التوكن إذا تم توفيره
    if (token !== undefined) {
      updateData.token = token;
    }

    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'لا توجد بيانات للتحديث'
      });
    }

    const updatedClient = await prisma.client.update({
      where: { id: parseInt(clientId) },
      data: updateData,
      select: {
        id: true,
        clientCode: true,
        clientName: true,
        token: true,
        appName: true,
        ipAddress: true,
        isActive: true
      }
    });

    console.log(`✅ Client updated successfully: ${updatedClient.clientName}`);

    res.json({
      success: true,
      message: 'تم تحديث البيانات بنجاح',
      client: updatedClient
    });
  } catch (error) {
    console.error('Client update error:', error);
    res.status(500).json({ success: false, message: 'خطأ في تحديث البيانات' });
  }
});

// Users API - مع تنسيق مطابق للعميل
app.get('/api/users', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (page - 1) * limit;

    const where = search ? {
      OR: [
        { username: { contains: search, mode: 'insensitive' } },
        { loginName: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        select: {
          id: true,
          username: true,
          loginName: true,
          deviceId: true,
          device1: true,
          permissions: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: { clients: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    console.log(`✅ Returning ${users.length} users out of ${total} total`);
    res.json({
      data: users,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Users error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// تحديث مستخدم
app.put('/api/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { username, loginName, deviceId, device1, permissions, isActive } = req.body;

    console.log(`🔄 Updating user ${id}:`, { username, loginName, deviceId, device1, isActive });

    const updatedUser = await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        username,
        loginName,
        deviceId,
        device1,
        permissions,
        isActive,
        updatedAt: new Date()
      },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        device1: true,
        permissions: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    });

    console.log(`✅ User ${id} updated successfully`);
    res.json(updatedUser);
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ error: 'فشل في تحديث المستخدم' });
  }
});

// إضافة مستخدم جديد
app.post('/api/users', async (req, res) => {
  try {
    const { username, loginName, password, deviceId, device1, permissions, isActive } = req.body;

    console.log(`➕ Creating new user:`, { username, loginName });

    const bcrypt = require('bcrypt');
    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = await prisma.user.create({
      data: {
        username,
        loginName,
        password: hashedPassword,
        deviceId,
        device1,
        permissions,
        isActive: isActive !== undefined ? isActive : true
      },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        device1: true,
        permissions: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    });

    console.log(`✅ User created successfully with ID: ${newUser.id}`);
    res.json(newUser);
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ error: 'فشل في إنشاء المستخدم' });
  }
});

// تحديث كلمة مرور المستخدم
app.put('/api/users/:id/password', async (req, res) => {
  try {
    const { id } = req.params;
    const { currentPassword, newPassword } = req.body;

    console.log(`🔐 Updating password for user ${id}`);

    // جلب المستخدم الحالي
    const user = await prisma.user.findUnique({
      where: { id: parseInt(id) }
    });

    if (!user) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }

    // التحقق من كلمة المرور الحالية
    const bcrypt = require('bcrypt');
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);

    if (!isCurrentPasswordValid) {
      return res.status(400).json({ error: 'كلمة المرور الحالية غير صحيحة' });
    }

    // تشفير كلمة المرور الجديدة
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // تحديث كلمة المرور
    await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        password: hashedNewPassword,
        updatedAt: new Date()
      }
    });

    console.log(`✅ Password updated successfully for user ${id}`);
    res.json({ message: 'تم تحديث كلمة المرور بنجاح' });
  } catch (error) {
    console.error('Update password error:', error);
    res.status(500).json({ error: 'فشل في تحديث كلمة المرور' });
  }
});

// تحديث معرف جهاز2 للمستخدم
app.put('/api/users/:id/device', async (req, res) => {
  try {
    const { id } = req.params;
    const { device1 } = req.body;

    console.log(`📱 Updating device1 for user ${id}:`, device1);

    // جلب المستخدم الحالي
    const user = await prisma.user.findUnique({
      where: { id: parseInt(id) }
    });

    if (!user) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }

    // تحديث معرف الجهاز2
    const updatedUser = await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        device1: device1 || null,
        updatedAt: new Date()
      }
    });

    console.log(`✅ Device1 updated successfully for user ${id}`);
    res.json({
      message: 'تم تحديث معرف الجهاز2 بنجاح',
      user: {
        id: updatedUser.id,
        device1: updatedUser.device1
      }
    });
  } catch (error) {
    console.error('Update device1 error:', error);
    res.status(500).json({ error: 'فشل في تحديث معرف الجهاز2' });
  }
});

// حذف مستخدم
app.delete('/api/users/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`🗑️ Deleting user ${id}`);

    await prisma.user.delete({
      where: { id: parseInt(id) }
    });

    console.log(`✅ User ${id} deleted successfully`);
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ error: 'فشل في حذف المستخدم' });
  }
});

// Clients API - مع تصفية حسب المستخدم
app.get('/api/clients', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', status, userId } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    // تصفية العملاء حسب المستخدم والصلاحيات
    if (userId) {
      const currentUser = await prisma.user.findUnique({
        where: { id: parseInt(userId) }
      });

      // التحقق من صلاحيات المدير الكامل (hash8080 فقط)
      const isSystemAdmin = currentUser?.loginName === 'hash8080' ||
        (currentUser?.permissions?.isAdmin === true &&
         currentUser?.permissions?.users?.read === true &&
         currentUser?.permissions?.users?.create === true &&
         currentUser?.permissions?.users?.delete === true);

      console.log(`🔍 User ${userId} (${currentUser?.loginName}) permissions check:`, {
        loginName: currentUser?.loginName,
        isAdmin: currentUser?.permissions?.isAdmin,
        isSystemAdmin: isSystemAdmin,
        permissions: currentUser?.permissions
      });

      // hash8080 يرى جميع العملاء، باقي المستخدمين يرون عملاءهم فقط
      if (!isSystemAdmin) {
        where.userId = parseInt(userId);
        console.log(`🔒 Filtering clients for user ${userId} (${currentUser?.loginName}) - showing own clients only`);
      } else {
        console.log(`👑 User ${userId} (${currentUser?.loginName}) is system admin - showing all clients`);
      }
    }

    if (search) {
      where.OR = [
        { clientName: { contains: search, mode: 'insensitive' } },
        { appName: { contains: search, mode: 'insensitive' } },
        { cardNumber: { contains: search } },
        { clientCode: { equals: parseInt(search) || 0 } }
      ];
    }

    if (status) {
      where.status = parseInt(status);
    }

    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          user: { select: { id: true, username: true, loginName: true } }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.client.count({ where })
    ]);

    console.log(`✅ Returning ${clients.length} clients out of ${total} total for user ${userId}`);
    res.json({
      data: clients,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Clients error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// إضافة عميل جديد
app.post('/api/clients', async (req, res) => {
  try {
    const { clientName, appName, cardNumber, password, ipAddress, status, userId } = req.body;

    console.log(`➕ Creating new client:`, { clientName, appName, userId });

    const bcrypt = require('bcrypt');
    const hashedPassword = await bcrypt.hash(password, 10);

    // توليد رمز العميل تلقائياً
    const lastClient = await prisma.client.findFirst({
      orderBy: { clientCode: 'desc' }
    });
    const nextClientCode = (lastClient?.clientCode || 0) + 1;

    // توليد التوكن المشفر
    const generateToken = () => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let token = '';
      for (let i = 0; i < 12; i++) {
        token += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return token;
    };

    const newClient = await prisma.client.create({
      data: {
        clientName,
        appName,
        cardNumber,
        password: hashedPassword,
        ipAddress,
        status: status || 1,
        clientCode: nextClientCode,
        token: generateToken(),
        userId: userId ? parseInt(userId) : null
      },
      include: {
        user: { select: { id: true, username: true, loginName: true } }
      }
    });

    console.log(`✅ Client created successfully with ID: ${newClient.id}, userId: ${newClient.userId}`);
    res.json(newClient);
  } catch (error) {
    console.error('Create client error:', error);
    res.status(500).json({ error: 'فشل في إنشاء العميل' });
  }
});

// تحديث عميل
app.put('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { clientName, appName, cardNumber, password, ipAddress, status } = req.body;

    console.log(`🔄 Updating client ${id}:`, { clientName, appName });

    const updateData = {
      clientName,
      appName,
      cardNumber,
      ipAddress,
      status,
      updatedAt: new Date()
    };

    // تشفير كلمة المرور إذا تم تغييرها
    if (password) {
      const bcrypt = require('bcrypt');
      updateData.password = await bcrypt.hash(password, 10);
    }

    const updatedClient = await prisma.client.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        user: { select: { id: true, username: true, loginName: true } }
      }
    });

    console.log(`✅ Client ${id} updated successfully`);
    res.json(updatedClient);
  } catch (error) {
    console.error('Update client error:', error);
    res.status(500).json({ error: 'فشل في تحديث العميل' });
  }
});

// حذف عميل
app.delete('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`🗑️ Deleting client ${id}`);

    await prisma.client.delete({
      where: { id: parseInt(id) }
    });

    console.log(`✅ Client ${id} deleted successfully`);
    res.json({ message: 'تم حذف العميل بنجاح' });
  } catch (error) {
    console.error('Delete client error:', error);
    res.status(500).json({ error: 'فشل في حذف العميل' });
  }
});

// Agents API - مع تنسيق مطابق للعميل
app.get('/api/agents', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', agencyType } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (search) {
      where.OR = [
        { agentName: { contains: search, mode: 'insensitive' } },
        { agencyName: { contains: search, mode: 'insensitive' } },
        { loginName: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (agencyType) {
      where.agencyType = agencyType;
    }

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        orderBy: { createdAt: 'desc' }
      }),
      prisma.agent.count({ where })
    ]);

    console.log(`✅ Returning ${agents.length} agents out of ${total} total`);
    res.json({
      data: agents,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Agents error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// Data Records API - مع تنسيق مطابق للعميل
app.get('/api/data-records', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', agentId, clientId, status } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (search) {
      where.OR = [
        { agentReference: { equals: parseInt(search) || 0 } },
        { agent: { agentName: { contains: search, mode: 'insensitive' } } },
        { client: { clientName: { contains: search, mode: 'insensitive' } } }
      ];
    }

    if (agentId) {
      where.agentId = parseInt(agentId);
    }

    if (clientId) {
      where.clientId = parseInt(clientId);
    }

    if (status !== undefined) {
      where.operationStatus = parseInt(status);
    }

    const [dataRecords, total] = await Promise.all([
      prisma.dataRecord.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          agent: { select: { agentName: true } },
          client: { select: { clientName: true } }
        },
        orderBy: { operationDate: 'desc' }
      }),
      prisma.dataRecord.count({ where })
    ]);

    console.log(`✅ Returning ${dataRecords.length} data records out of ${total} total`);
    res.json({
      data: dataRecords,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Data records error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// Dashboard Stats - إحصائيات شاملة
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    const [totalUsers, totalClients, totalAgents, totalDataRecords, totalSecurity] = await Promise.all([
      prisma.user.count(),
      prisma.client.count(),
      prisma.agent.count(),
      prisma.dataRecord.count(),
      prisma.loginAttempt.count()
    ]);

    const stats = {
      totalUsers,
      totalClients,
      totalAgents,
      totalDataRecords,
      totalSecurity,
      systemHealth: 'excellent'
    };
    console.log('✅ Dashboard stats:', stats);
    res.json(stats);
  } catch (error) {
    console.error('Dashboard error:', error);
    res.json({
      totalUsers: 0,
      totalClients: 0,
      totalAgents: 0,
      totalDataRecords: 0,
      totalSecurity: 0
    });
  }
});

// Dashboard Recent Activity
app.get('/api/dashboard/recent-activity', async (req, res) => {
  try {
    const recentClients = await prisma.client.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: { user: { select: { username: true } } }
    });

    const activities = recentClients.map(client => ({
      id: `client_${client.id}`,
      type: 'client_created',
      user: client.user?.username || 'مجهول',
      description: `إضافة عميل: ${client.clientName}`,
      timestamp: client.createdAt.toISOString()
    }));

    console.log(`✅ Recent activities: ${activities.length}`);
    res.json({ activities, total: activities.length });
  } catch (error) {
    console.error('Recent activity error:', error);
    res.json({ activities: [], total: 0 });
  }
});

// Security Stats - إحصائيات الأمان الحقيقية
app.get('/api/security/stats', async (req, res) => {
  try {
    const [
      totalAttempts,
      successfulAttempts,
      failedAttempts,
      todayAttempts,
      uniqueIPs
    ] = await Promise.all([
      prisma.loginAttempt.count(),
      prisma.loginAttempt.count({ where: { success: true } }),
      prisma.loginAttempt.count({ where: { success: false } }),
      prisma.loginAttempt.count({
        where: {
          timestamp: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
      prisma.loginAttempt.groupBy({
        by: ['ipAddress'],
        _count: { ipAddress: true }
      })
    ]);

    const stats = {
      totalAttempts,
      successfulLogins: successfulAttempts,
      failedAttempts,
      todayAttempts,
      uniqueIPs: uniqueIPs.length,
      successRate: totalAttempts > 0 ? ((successfulAttempts / totalAttempts) * 100).toFixed(1) : 0,
      suspiciousActivity: failedAttempts > 10 ? Math.floor(failedAttempts / 10) : 0
    };

    console.log('✅ Security stats:', stats);
    res.json(stats);
  } catch (error) {
    console.error('Security stats error:', error);
    res.json({
      totalAttempts: 0,
      successfulLogins: 0,
      failedAttempts: 0,
      todayAttempts: 0,
      uniqueIPs: 0,
      successRate: 0,
      suspiciousActivity: 0
    });
  }
});

// Security Advanced Stats
app.get('/api/security/advanced-stats', async (req, res) => {
  try {
    const totalUsers = await prisma.user.count();
    res.json({
      suspiciousIPs: 0,
      blockedIPs: 0,
      totalLoginAttempts: totalUsers * 2,
      recentAttacks: [],
      lastSecurityScan: new Date().toISOString(),
      systemStatus: 'operational'
    });
  } catch (error) {
    res.json({ suspiciousIPs: 0, blockedIPs: 0, totalLoginAttempts: 0, recentAttacks: [] });
  }
});

// Security Login Attempts - محاولات الدخول الحقيقية
app.get('/api/security/login-attempts', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', success, userType } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (search) {
      where.OR = [
        { ipAddress: { contains: search } },
        { deviceId: { contains: search } },
        { userId: { equals: parseInt(search) || 0 } }
      ];
    }

    if (success !== undefined && success !== '') {
      where.success = success === 'true';
    }

    if (userType) {
      where.userType = userType;
    }

    const [attempts, total] = await Promise.all([
      prisma.loginAttempt.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          user: {
            select: { username: true, loginName: true }
          },
          agent: {
            select: { agentName: true }
          }
        },
        orderBy: { timestamp: 'desc' }
      }),
      prisma.loginAttempt.count({ where })
    ]);


    // تنسيق البيانات للعرض
    const formattedAttempts = attempts.map(attempt => ({
      id: attempt.id.toString(),
      type: attempt.success ? 'success' : 'failed',
      username: attempt.user?.username || attempt.user?.loginName || `User ${attempt.userId}`,
      ip: attempt.ipAddress,
      timestamp: attempt.timestamp.toISOString(),
      userAgent: 'System Login',
      deviceId: attempt.deviceId || 'unknown',
      reason: attempt.success ? null : 'Authentication failed',
      userType: attempt.userType,
      agentName: attempt.agent?.agentName
    }));

    console.log(`✅ Returning ${formattedAttempts.length} login attempts out of ${total} total`);
    res.json({
      attempts: formattedAttempts,
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Login attempts error:', error);
    res.json({ attempts: [], total: 0, page: 1, limit: 10, totalPages: 0 });
  }
});

// ========================================
// APIs الخارجية للمطورين
// ========================================

// API للتحقق المباشر (للمطورين الخارجيين)
app.post('/api/external/verify-direct', async (req, res) => {
  try {
    const {
      agent_login_name,
      agent_login_password,
      client_code,
      client_token
    } = req.body;

    console.log('🔍 External verify-direct request:', { agent_login_name, client_code });

    // التحقق من وجود جميع البيانات المطلوبة
    if (!agent_login_name || !agent_login_password || !client_code || !client_token) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: agent_login_name, agent_login_password, client_code, client_token',
        error_code: 'VALIDATION_ERROR'
      });
    }

    // الخطوة 1: التحقق من الوكيل
    const agent = await prisma.agent.findFirst({
      where: {
        loginName: agent_login_name,
        isActive: true
      }
    });

    if (!agent) {
      console.log('❌ Agent not found:', agent_login_name);
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    // التحقق من كلمة مرور الوكيل
    const bcrypt = require('bcrypt');
    const isPasswordValid = await bcrypt.compare(agent_login_password, agent.loginPassword);

    if (!isPasswordValid) {
      console.log('❌ Invalid agent password:', agent_login_name);
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    // الخطوة 2: التحقق من العميل
    const client = await prisma.client.findFirst({
      where: {
        clientCode: parseInt(client_code)
      }
    });

    if (!client) {
      console.log('❌ Client not found:', client_code);
      return res.status(404).json({
        status: 'client_error'
      });
    }

    // الخطوة 3: التحقق من توكن العميل
    const isTokenValid = client.token === client_token;

    if (!isTokenValid) {
      console.log('❌ Invalid client token:', client_code);
      return res.status(401).json({
        status: 'client_error'
      });
    }

    // تسجيل العملية الناجحة
    await prisma.dataRecord.create({
      data: {
        agentId: agent.id,
        clientId: client.id,
        clientCode: client_code.toString(),
        agentCode: agent.agentCode,
        operationType: 'external_verify',
        operationStatus: 1,
        operationDate: new Date(),
        ipAddress: req.ip || 'unknown'
      }
    });

    console.log('✅ External verify-direct successful:', { agent_login_name, client_code, client_status: client.status });

    res.json({
      status: 'success',
      client_status: client.status,
      data: {
        agent_info: {
          agent_name: agent.agentName,
          agency_name: agent.agencyName,
          agency_type: agent.agencyType,
          agent_id: agent.agentCode
        },
        client_info: {
          client_code: client.clientCode,
          client_name: client.clientName,
          app_name: client.appName,
          status: client.status
        },
        verification_result: {
          agent_verified: true,
          client_verified: true,
          timestamp: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('External verify-direct error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Verification service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// إحصائيات خارجية للمطورين
app.get('/api/external/stats', async (req, res) => {
  try {
    const [totalClients, activeClients, totalAgents, activeAgents, totalOperations] = await Promise.all([
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.agent.count(),
      prisma.agent.count({ where: { isActive: true } }),
      prisma.dataRecord.count()
    ]);

    res.json({
      success: true,
      data: {
        total_clients: totalClients,
        active_clients: activeClients,
        blocked_clients: totalClients - activeClients,
        total_agents: totalAgents,
        active_agents: activeAgents,
        inactive_agents: totalAgents - activeAgents,
        total_operations: totalOperations,
        server_status: 'operational',
        last_updated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('External stats error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الإحصائيات'
    });
  }
});

// صحة النظام للمطورين
app.get('/api/external/health', async (req, res) => {
  try {
    const dbTest = await prisma.user.count();

    res.json({
      success: true,
      status: 'healthy',
      database: 'connected',
      server: 'operational',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      status: 'unhealthy',
      error: error.message
    });
  }
});

// Static files
app.use(express.static(path.join(__dirname, '../client/dist')));

// React app fallback
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API not found' });
  }
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Working Server running on http://0.0.0.0:${PORT}`);
  console.log(`🌐 External: http://***********:${PORT}`);
  console.log(`🏠 Local: http://localhost:${PORT}`);
});

console.log('🎯 Working Server initialized');
