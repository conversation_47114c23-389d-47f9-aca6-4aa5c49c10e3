{"name": "yem-client-server", "version": "1.0.0", "description": "Backend server for YemClient Management System", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js", "db:generate": "npx prisma generate", "db:migrate": "npx prisma migrate dev", "db:studio": "npx prisma studio", "db:seed": "node prisma/seed.js"}, "dependencies": {"@prisma/client": "^5.6.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^7.2.0", "http-proxy-middleware": "^3.0.5", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.16.3", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1", "prisma": "^5.6.0"}}