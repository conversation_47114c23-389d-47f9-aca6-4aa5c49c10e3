@echo off
chcp 65001 >nul
title نظام إدارة العملاء والوكلاء - الخادم المستقر

echo ========================================
echo    🚀 تشغيل نظام إدارة العملاء والوكلاء
echo         الخادم المستقر والآمن
echo ========================================
echo.

echo 📋 فحص النظام...
echo.

REM التحقق من Node.js
echo 1. فحص Node.js...
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do echo ✅ Node.js: %%i
)

REM التحقق من PostgreSQL
echo 2. فحص PostgreSQL...
pg_isready -h localhost -p 5432 >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ PostgreSQL غير متاح على localhost:5432
    echo تأكد من تشغيل PostgreSQL
) else (
    echo ✅ PostgreSQL: متاح
)

REM التحقق من مجلد المشروع
echo 3. فحص مجلد المشروع...
if not exist "C:\yemclinet\server\index.js" (
    echo ❌ ملف الخادم غير موجود
    echo تأكد من وجود: C:\yemclinet\server\index.js
    pause
    exit /b 1
) else (
    echo ✅ ملف الخادم: موجود
)

REM التحقق من ملف .env
echo 4. فحص إعدادات قاعدة البيانات...
if not exist "C:\yemclinet\server\.env" (
    echo ⚠️ ملف .env غير موجود
    echo سيتم استخدام الإعدادات الافتراضية
) else (
    echo ✅ ملف .env: موجود
)

REM التحقق من node_modules
echo 5. فحص المكتبات المطلوبة...
if not exist "C:\yemclinet\server\node_modules" (
    echo ⚠️ المكتبات غير مثبتة
    echo تثبيت المكتبات...
    cd /d C:\yemclinet\server
    call npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
) else (
    echo ✅ المكتبات: مثبتة
)

echo.
echo ========================================
echo           🔧 إعدادات النظام
echo ========================================
echo 🌐 الوصول الخارجي: http://***********:8080
echo 🏠 الوصول المحلي: http://localhost:8080
echo 🔐 الحماية: مفعلة
echo 🛡️ Rate Limiting: نشط
echo 🚨 حماية التهديدات: نشطة
echo 📊 قاعدة البيانات: PostgreSQL
echo ========================================
echo.

REM إيقاف أي خوادم سابقة
echo 🔄 إيقاف الخوادم السابقة...
taskkill /F /IM node.exe /T >nul 2>&1

REM انتظار قصير
timeout /t 2 /nobreak >nul

echo.
echo 🚀 تشغيل الخادم المستقر...
echo ========================================
echo.

REM الانتقال لمجلد الخادم
cd /d C:\yemclinet\server

REM تشغيل الخادم
echo 📡 بدء تشغيل الخادم...
node index.js

REM في حالة توقف الخادم
echo.
echo ⚠️ توقف الخادم
echo.
echo 💡 نصائح لحل المشاكل:
echo 1. تحقق من تشغيل PostgreSQL
echo 2. تحقق من ملف .env
echo 3. تحقق من المنفذ 8080 (قد يكون مستخدم)
echo 4. راجع رسائل الخطأ أعلاه
echo.
echo ========================================
echo         انتهى تشغيل الخادم
echo ========================================
pause
