const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// تسجيل الدخول
router.post('/login', [
  body('loginName').notEmpty().withMessage('Login name is required'),
  body('password').notEmpty().withMessage('Password is required'),
  body('deviceId').notEmpty().withMessage('Device ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { loginName, password, deviceId } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';

    console.log('🔐 Login attempt:', {
      loginName,
      deviceId: deviceId.substring(0, 10) + '...',
      ip: clientIP
    });

    // البحث عن المستخدم
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { loginName: loginName },
          { username: loginName }
        ],
        isActive: true
      }
    });

    if (!user) {
      console.log('❌ User not found:', loginName);

      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          ipAddress: clientIP,
          deviceId: deviceId,
          success: false,
          userType: 'user',
          failureReason: 'User not found'
        }
      });

      return res.status(401).json({
        success: false,
        error: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      console.log('❌ Invalid password for:', loginName);

      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          userId: user.id,
          ipAddress: clientIP,
          deviceId: deviceId,
          success: false,
          userType: 'user',
          failureReason: 'Invalid password'
        }
      });

      return res.status(401).json({
        success: false,
        error: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    // التحقق من الجهاز - دعم device1 و deviceId
    let isDeviceAuthorized = false;
    let authorizedDevices = [];

    // فحص device1 أولاً (العمود الجديد)
    if (user.device1) {
      authorizedDevices.push(user.device1);
      if (user.device1 === deviceId) {
        isDeviceAuthorized = true;
      }
    }

    // فحص deviceId (العمود القديم) إذا لم يتم العثور على الجهاز في device1
    if (!isDeviceAuthorized && user.deviceId) {
      const oldDevices = user.deviceId.includes(',')
        ? user.deviceId.split(',').map(id => id.trim())
        : [user.deviceId];

      authorizedDevices = [...authorizedDevices, ...oldDevices];

      if (oldDevices.includes(deviceId)) {
        isDeviceAuthorized = true;
      }
    }

    // إذا لم يكن هناك أجهزة مسجلة، السماح بالدخول وتسجيل الجهاز
    if (!user.device1 && !user.deviceId) {
      console.log('📱 No devices registered, registering current device');

      // تحديث device1 بالجهاز الحالي
      await prisma.user.update({
        where: { id: user.id },
        data: { device1: deviceId }
      });

      isDeviceAuthorized = true;
      authorizedDevices = [deviceId];
    }

    // إذا كان هناك جهاز واحد فقط مسجل، السماح بتسجيل جهاز ثاني
    if (!isDeviceAuthorized && authorizedDevices.length === 1) {
      console.log('📱 Adding second device for user:', user.username);

      // إضافة الجهاز الثاني إلى device1 إذا كان فارغاً
      if (!user.device1 && user.deviceId) {
        await prisma.user.update({
          where: { id: user.id },
          data: { device1: deviceId }
        });

        isDeviceAuthorized = true;
        authorizedDevices.push(deviceId);
        console.log('✅ Second device registered in device1');
      }
      // أو إضافة الجهاز الثاني إلى deviceId إذا كان device1 مشغولاً
      else if (user.device1 && !user.deviceId) {
        await prisma.user.update({
          where: { id: user.id },
          data: { deviceId: deviceId }
        });

        isDeviceAuthorized = true;
        authorizedDevices.push(deviceId);
        console.log('✅ Second device registered in deviceId');
      }
    }

    if (!isDeviceAuthorized) {
      console.log('❌ Device not authorized:', deviceId);
      console.log('   Authorized devices:', authorizedDevices);

      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          userId: user.id,
          ipAddress: clientIP,
          deviceId: deviceId,
          success: false,
          userType: 'user',
          failureReason: 'Device not authorized'
        }
      });

      return res.status(403).json({
        success: false,
        error: 'الجهاز غير مصرح له بالدخول',
        message: 'هذا الحساب مرتبط بأجهزة أخرى. يرجى تسجيل الدخول من أحد الأجهزة المصرح بها.',
        authorizedDevices: authorizedDevices.map(d => d.substring(0, 10) + '...')
      });
    }

    // إنشاء JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        loginName: user.loginName,
        deviceId: deviceId
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    // تسجيل محاولة دخول ناجحة
    await prisma.loginAttempt.create({
      data: {
        userId: user.id,
        ipAddress: clientIP,
        deviceId: deviceId,
        success: true,
        userType: 'user'
      }
    });

    console.log('✅ Login successful for:', user.username);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions,
        isActive: user.isActive
      },
      token: token
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم'
    });
  }
});

// تسجيل الخروج
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    // يمكن إضافة منطق إضافي هنا مثل إلغاء التوكن
    res.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في تسجيل الخروج'
    });
  }
});

// الحصول على معلومات المستخدم الحالي
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        username: true,
        loginName: true,
        permissions: true,
        isActive: true,
        device1: true,
        deviceId: true,
        createdAt: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'المستخدم غير موجود'
      });
    }

    res.json({
      success: true,
      user: user
    });
  } catch (error) {
    console.error('Get user info error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في جلب معلومات المستخدم'
    });
  }
});

// تحديث معلومات الجهاز
router.post('/update-device', authenticateToken, [
  body('deviceId').notEmpty().withMessage('Device ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { deviceId } = req.body;
    const userId = req.user.id;

    // تحديث device1 بالجهاز الجديد
    await prisma.user.update({
      where: { id: userId },
      data: { device1: deviceId }
    });

    console.log(`📱 Device updated for user ${req.user.username}: ${deviceId}`);

    res.json({
      success: true,
      message: 'تم تحديث معلومات الجهاز بنجاح'
    });

  } catch (error) {
    console.error('Update device error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في تحديث معلومات الجهاز'
    });
  }
});

module.exports = router;
