package routes

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthRoutes - مطابق تماماً لـ server/routes/auth.js
func AuthRoutes(router *gin.RouterGroup, db *gorm.DB) {
	// تسجيل الدخول - مطابق لـ auth.js
	router.POST("/login", func(c *gin.Context) {
		var loginData struct {
			LoginName string `json:"loginName" binding:"required"`
			Password  string `json:"password" binding:"required"`
			DeviceID  string `json:"deviceId" binding:"required"`
		}

		if err := c.ShouldBindJSON(&loginData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"errors":  []string{"جميع الحقول مطلوبة"},
			})
			return
		}

		clientIP := c.ClientIP()
		log.Printf("🔐 Login attempt: %s, %s, %s", loginData.LoginName, loginData.DeviceID[:10]+"...", clientIP)

		// إذا لم تكن قاعدة البيانات متصلة، استخدم mock data
		if db == nil {
			if loginData.LoginName == "hash8080" && loginData.Password == "yemen123456" {
				c.JSON(http.StatusOK, gin.H{
					"success": true,
					"message": "تم تسجيل الدخول بنجاح",
					"user": gin.H{
						"id":          1,
						"username":    "hash8080",
						"loginName":   "hash8080",
						"permissions": gin.H{"isAdmin": true},
						"isActive":    true,
					},
					"token": fmt.Sprintf("token_%d_%d", 1, time.Now().Unix()),
				})
				return
			}
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "اسم المستخدم أو كلمة المرور غير صحيحة",
			})
			return
		}

		// البحث في قاعدة البيانات - مطابق لـ auth.js
		var user struct {
			ID          uint    `json:"id"`
			Username    string  `json:"username"`
			LoginName   string  `json:"loginName"`
			Password    string  `json:"password"`
			DeviceID    *string `json:"deviceId"`
			Device1     *string `json:"device1"`
			Permissions *string `json:"permissions"`
			IsActive    bool    `json:"isActive"`
		}

		err := db.Table("users").Where("(login_name = ? OR username = ?) AND is_active = ?",
			loginData.LoginName, loginData.LoginName, true).First(&user).Error

		if err != nil {
			log.Printf("❌ User not found: %s", loginData.LoginName)

			// البحث بدون شرط isActive للتشخيص
			var inactiveUser struct {
				Username  string `json:"username"`
				IsActive  bool   `json:"isActive"`
				LoginName string `json:"loginName"`
			}
			inactiveErr := db.Table("users").Where("login_name = ? OR username = ?",
				loginData.LoginName, loginData.LoginName).First(&inactiveUser).Error

			if inactiveErr == nil {
				log.Printf("⚠️ User found but inactive: %s, active: %t", inactiveUser.Username, inactiveUser.IsActive)

				// تسجيل محاولة دخول فاشلة
				db.Table("login_attempts").Create(map[string]interface{}{
					"user_id":        nil,
					"ip_address":     clientIP,
					"device_id":      loginData.DeviceID,
					"success":        false,
					"user_type":      "user",
					"failure_reason": "User inactive",
					"created_at":     time.Now(),
				})

				c.JSON(http.StatusUnauthorized, gin.H{
					"success": false,
					"error":   "المستخدم غير نشط",
				})
				return
			}

			// تسجيل محاولة دخول فاشلة
			db.Table("login_attempts").Create(map[string]interface{}{
				"user_id":        nil,
				"ip_address":     clientIP,
				"device_id":      loginData.DeviceID,
				"success":        false,
				"user_type":      "user",
				"failure_reason": "User not found",
				"created_at":     time.Now(),
			})

			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "اسم المستخدم أو كلمة المرور غير صحيحة",
			})
			return
		}

		log.Printf("✅ User found: %s (ID: %d)", user.Username, user.ID)

		// التحقق من كلمة المرور
		err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(loginData.Password))
		if err != nil {
			log.Printf("❌ Invalid password for: %s", loginData.LoginName)

			// تسجيل محاولة دخول فاشلة
			db.Table("login_attempts").Create(map[string]interface{}{
				"user_id":        user.ID,
				"ip_address":     clientIP,
				"device_id":      loginData.DeviceID,
				"success":        false,
				"user_type":      "user",
				"failure_reason": "Invalid password",
				"created_at":     time.Now(),
			})

			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "اسم المستخدم أو كلمة المرور غير صحيحة",
			})
			return
		}

		// التحقق من الجهاز - مطابق لـ auth.js
		isDeviceAuthorized := false
		var authorizedDevices []string

		// فحص device1 أولاً (العمود الجديد)
		if user.Device1 != nil && *user.Device1 != "" {
			authorizedDevices = append(authorizedDevices, *user.Device1)
			if *user.Device1 == loginData.DeviceID {
				isDeviceAuthorized = true
			}
		}

		// فحص deviceId (العمود القديم)
		if user.DeviceID != nil && *user.DeviceID != "" {
			authorizedDevices = append(authorizedDevices, *user.DeviceID)
			if *user.DeviceID == loginData.DeviceID {
				isDeviceAuthorized = true
			}
		}

		log.Printf("🔍 Device check: authorized devices: %v, provided: %s", authorizedDevices, loginData.DeviceID)

		// إذا كان هناك أجهزة محفوظة ولم يطابق أي منها
		if len(authorizedDevices) > 0 && !isDeviceAuthorized {
			log.Printf("❌ Device not authorized for user %s", user.Username)

			// تسجيل محاولة دخول فاشلة
			db.Table("login_attempts").Create(map[string]interface{}{
				"user_id":        user.ID,
				"ip_address":     clientIP,
				"device_id":      loginData.DeviceID,
				"success":        false,
				"user_type":      "user",
				"failure_reason": "Device not authorized",
				"created_at":     time.Now(),
			})

			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "لا يمكن الوصول من الجهاز الحالي",
			})
			return
		}

		log.Printf("✅ Device check passed for user %s", user.Username)

		// Parse permissions
		var permissions interface{}
		if user.Permissions != nil {
			json.Unmarshal([]byte(*user.Permissions), &permissions)
		}

		// إنشاء JWT token (مبسط)
		token := fmt.Sprintf("token_%d_%d", user.ID, time.Now().Unix())

		// تسجيل محاولة دخول ناجحة
		db.Table("login_attempts").Create(map[string]interface{}{
			"user_id":    user.ID,
			"ip_address": clientIP,
			"device_id":  loginData.DeviceID,
			"success":    true,
			"user_type":  "user",
			"created_at": time.Now(),
		})

		log.Printf("✅ Login successful for: %s", user.Username)

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "تم تسجيل الدخول بنجاح",
			"user": gin.H{
				"id":          user.ID,
				"username":    user.Username,
				"loginName":   user.LoginName,
				"permissions": permissions,
				"isActive":    user.IsActive,
			},
			"token": token,
		})
	})

	// تسجيل الخروج
	router.POST("/logout", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "تم تسجيل الخروج بنجاح",
		})
	})

	// معلومات المستخدم الحالي
	router.GET("/me", func(c *gin.Context) {
		// TODO: تطبيق JWT authentication
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"user": gin.H{
				"id":       1,
				"username": "hash8080",
			},
		})
	})
}
