const express = require('express');
const cors = require('cors');
const path = require('path');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

const app = express();
const PORT = 8080;
const prisma = new PrismaClient();

console.log('🔐 Starting Secure Working Server...');

// إعدادات الحماية الأساسية
const helmetConfig = helmet({
  hidePoweredBy: true,
  frameguard: { action: 'deny' },
  noSniff: true,
  xssFilter: true,
  referrerPolicy: { policy: 'no-referrer' }
});

// Rate Limiting
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 100, // 100 طلب كحد أقصى
  message: { error: 'تم تجاوز الحد المسموح من الطلبات لاتحاول' },
  standardHeaders: false,
  legacyHeaders: false
});

const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 5, // 5 محاولات فقط
  message: { error: 'تم تجاوز محاولات تسجيل الدخول المسموحة لاتحاول' },
  standardHeaders: false,
  legacyHeaders: false
});

const apiLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 دقائق
  max: 20, // 20 طلب كحد أقصى للـ APIs
  message: { error: 'تم تجاوز الحد المسموح للعمليات الحساسة لاتحاول' },
  standardHeaders: false,
  legacyHeaders: false
});

// Middleware للحماية
app.use(helmetConfig);
app.use(generalLimiter);

// إخفاء معلومات الخادم
app.use((req, res, next) => {
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');
  res.setHeader('Server', 'Apache/2.4.41');
  res.setHeader('X-Powered-By', 'PHP/7.4.3');
  next();
});

// منع الوصول للملفات الحساسة
app.use((req, res, next) => {
  const blockedPaths = [
    '/.env', '/package.json', '/package-lock.json', '/node_modules',
    '/.git', '/prisma', '/logs', '/config', '/.vscode', '/src'
  ];

  if (blockedPaths.some(blocked => req.path.toLowerCase().includes(blocked))) {
    console.log(`🚨 محاولة وصول لملف حساس: ${req.path} من IP: ${req.ip}`);
    return res.status(404).json({ error: 'الصفحة غير موجودة لاتحاول' });
  }
  next();
});

// منع SQL Injection
app.use((req, res, next) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /(\'|\"|;|--|\*|\|)/,
    /(\bUNION\b.*\bSELECT\b)/i
  ];

  const checkForSQLInjection = (obj) => {
    for (let key in obj) {
      if (typeof obj[key] === 'string') {
        for (let pattern of sqlPatterns) {
          if (pattern.test(obj[key])) {
            console.log(`🚨 محاولة SQL Injection من IP: ${req.ip}`);
            return true;
          }
        }
      }
    }
    return false;
  };

  if (checkForSQLInjection(req.query) || checkForSQLInjection(req.body)) {
    return res.status(400).json({ error: 'بيانات غير صحيحة لاتحاول' });
  }
  next();
});

// تتبع النشاط المشبوه
app.use((req, res, next) => {
  const suspiciousAgents = [/sqlmap/i, /nikto/i, /nmap/i, /masscan/i, /zap/i, /burp/i];
  const userAgent = req.get('User-Agent') || '';

  if (suspiciousAgents.some(pattern => pattern.test(userAgent))) {
    console.log(`🚨 User Agent مشبوه: ${userAgent} من IP: ${req.ip}`);
    return res.status(403).json({ error: 'وصول مرفوض لاتحاول' });
  }
  next();
});

// CORS مع إعدادات آمنة
app.use(cors({
  origin: ['http://localhost:3000', 'http://***********:8080', 'http://**************:8080'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Middleware للتحقق من التوكن (للـ APIs الحساسة فقط)
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  const deviceId = req.headers['x-device-id'];

  // السماح ببعض المسارات بدون توكن
  const publicPaths = ['/health', '/api/test', '/api/client/login', '/api/agent/login'];
  if (publicPaths.includes(req.path)) {
    return next();
  }

  if (!token) {
    return res.status(401).json({ error: 'Access token required لاتحاول' });
  }

  if (!deviceId) {
    return res.status(401).json({ error: 'Device ID required لاتحاول' });
  }

  jwt.verify(token, JWT_SECRET, async (err, decoded) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token لاتحاول' });
    }

    try {
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: { id: true, username: true, loginName: true, deviceId: true, isActive: true }
      });

      if (!user || !user.isActive) {
        return res.status(401).json({ error: 'User not found or inactive لاتحاول' });
      }

      // التحقق من Device ID
      if (user.deviceId && user.deviceId !== deviceId) {
        return res.status(403).json({ error: 'Device not authorized لاتحاول' });
      }

      req.user = user;
      req.deviceId = deviceId;
      next();
    } catch (error) {
      return res.status(500).json({ error: 'Authentication error لاتحاول' });
    }
  });
};

// Logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Test database connection
prisma.$connect().then(() => {
  console.log('✅ Database connected');
  prisma.user.count().then(count => console.log(`👥 Users: ${count}`));
  prisma.client.count().then(count => console.log(`🏢 Clients: ${count}`));
  prisma.agent.count().then(count => console.log(`🤝 Agents: ${count}`));
}).catch(err => console.error('❌ Database error:', err));

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Authentication
app.post('/api/auth/login', async (req, res) => {
  const { loginName, password, deviceId } = req.body;
  console.log('🔐 Login:', { loginName, deviceId });

  try {
    console.log(`🔍 Searching for user with loginName: "${loginName}"`);

    const user = await prisma.user.findFirst({
      where: {
        OR: [{ loginName }, { username: loginName }],
        isActive: true
      },
      select: {
        id: true,
        username: true,
        loginName: true,
        password: true,
        deviceId: true,
        device1: true,
        permissions: true,
        isActive: true
      }
    });

    if (!user) {
      console.log(`❌ User not found: "${loginName}"`);

      // البحث بدون شرط isActive للتشخيص
      const inactiveUser = await prisma.user.findFirst({
        where: {
          OR: [{ loginName }, { username: loginName }]
        },
        select: {
          id: true,
          username: true,
          loginName: true,
          deviceId: true,
          device1: true,
          isActive: true
        }
      });

      if (inactiveUser) {
        console.log(`⚠️ User found but inactive: ${inactiveUser.username}, active: ${inactiveUser.isActive}`);
        return res.status(401).json({ success: false, error: 'المستخدم غير نشط' });
      }

      return res.status(401).json({ success: false, error: 'المستخدم غير موجود' });
    }

    console.log(`✅ User found: ${user.username} (ID: ${user.id})`);

    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) {
      return res.status(401).json({ success: false, error: 'كلمة المرور خاطئة' });
    }

    // التحقق من الجهاز - يدعم deviceId و device1
    console.log(`🔍 Device check: user.deviceId="${user.deviceId}", user.device1="${user.device1}", provided="${deviceId}"`);

    // التحقق من أن الجهاز مطابق لأحد العمودين
    const isDeviceAuthorized = (
      (user.deviceId && user.deviceId === deviceId) ||
      (user.device1 && user.device1 === deviceId)
    );

    // إذا كان هناك أجهزة محفوظة ولم يطابق أي منها
    if ((user.deviceId || user.device1) && !isDeviceAuthorized) {
      console.log(`❌ Device not authorized for user ${user.username}`);
      return res.status(401).json({
        success: false,
        error: 'لا يمكن الوصول من الجهاز الحالي'
      });
    }

    console.log(`✅ Device check passed for user ${user.username}`);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions,
       isActive: user.isActive  // ← المضاف
      },
      token: `token_${user.id}_${Date.now()}`
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, error: 'خطأ في الخادم' });
  }
});

// Client login API
app.post('/api/client/login', async (req, res) => {
  const { clientCode, password } = req.body;
  console.log('🏢 Client Login:', { clientCode, passwordLength: password?.length });

  try {
    console.log(`🔍 Searching for client with code: "${clientCode}"`);

    // أولاً، دعنا نتحقق من وجود العميل بدون شرط isActive
    const allClients = await prisma.client.findMany({
      select: {
        id: true,
        clientCode: true,
        clientName: true,
        status: true
      }
    });
    console.log('📋 All clients in database:', allClients);

    const client = await prisma.client.findFirst({
      where: {
        clientCode: parseInt(clientCode)
      },
      select: {
        id: true,
        clientCode: true,
        clientName: true,
        password: true,
        token: true,
        status: true,
        appName: true,
        ipAddress: true
      }
    });

    console.log('🔍 Found client:', client ? { id: client.id, clientCode: client.clientCode, clientName: client.clientName, status: client.status } : 'Not found');

    if (!client) {
      console.log(`❌ Client not found: ${clientCode}`);
      return res.status(401).json({
        success: false,
        message: 'رمز العميل غير صحيح'
      });
    }

    // التحقق من حالة العميل (status = 1 يعني نشط)
    if (client.status !== 1) {
      console.log(`❌ Client is not active: ${clientCode}, status: ${client.status}`);
      return res.status(401).json({
        success: false,
        message: 'حساب العميل غير نشط'
      });
    }

    console.log(`✅ Client found and active: ${client.clientName}`);

    // التحقق من كلمة المرور
    console.log(`🔐 Checking password for client: ${clientCode}`);
    console.log(`🔐 Password hash in DB: ${client.password?.substring(0, 20)}...`);
    console.log(`🔐 Input password: ${password}`);

    const isPasswordValid = await bcrypt.compare(password, client.password);
    console.log(`🔐 Password validation result: ${isPasswordValid}`);

    if (!isPasswordValid) {
      console.log(`❌ Invalid password for client: ${clientCode}`);
      return res.status(401).json({
        success: false,
        message: 'كلمة المرور غير صحيحة'
      });
    }

    console.log(`✅ Password valid for client: ${client.clientName}`);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      client: {
        id: client.id,
        clientCode: client.clientCode,
        clientName: client.clientName,
        token: client.token,
        appName: client.appName,
        ipAddress: client.ipAddress,
        status: client.status
      }
    });
  } catch (error) {
    console.error('Client login error:', error);
    res.status(500).json({ success: false, message: 'خطأ في الخادم' });
  }
});

// Client update API
app.put('/api/client/update', async (req, res) => {
  const { clientId, password, token } = req.body;
  console.log('🔄 Client Update:', { clientId, hasPassword: !!password, hasToken: !!token });

  try {
    const updateData = {};

    // تحديث كلمة المرور إذا تم توفيرها
    if (password) {
      const hashedPassword = await bcrypt.hash(password, 10);
      updateData.password = hashedPassword;
    }

    // تحديث رمز التوكن إذا تم توفيره
    if (token !== undefined) {
      updateData.token = token;
    }

    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'لا توجد بيانات للتحديث'
      });
    }

    const updatedClient = await prisma.client.update({
      where: { id: parseInt(clientId) },
      data: updateData,
      select: {
        id: true,
        clientCode: true,
        clientName: true,
        token: true,
        appName: true,
        ipAddress: true,
        isActive: true
      }
    });

    console.log(`✅ Client updated successfully: ${updatedClient.clientName}`);

    res.json({
      success: true,
      message: 'تم تحديث البيانات بنجاح',
      client: updatedClient
    });
  } catch (error) {
    console.error('Client update error:', error);
    res.status(500).json({ success: false, message: 'خطأ في تحديث البيانات' });
  }
});

// Users API - محمي
app.get('/api/users', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (page - 1) * limit;

    const where = search ? {
      OR: [
        { username: { contains: search, mode: 'insensitive' } },
        { loginName: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        select: {
          id: true,
          username: true,
          loginName: true,
          deviceId: true,
          device1: true,
          permissions: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: { clients: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    console.log(`✅ Returning ${users.length} users out of ${total} total`);
    res.json({
      data: users,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Users error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// تحديث مستخدم
app.put('/api/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { username, loginName, deviceId, device1, permissions, isActive } = req.body;

    console.log(`🔄 Updating user ${id}:`, { username, loginName, deviceId, device1, isActive });

    const updatedUser = await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        username,
        loginName,
        deviceId,
        device1,
        permissions,
        isActive,
        updatedAt: new Date()
      },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        device1: true,
        permissions: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    });

    console.log(`✅ User ${id} updated successfully`);
    res.json(updatedUser);
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ error: 'فشل في تحديث المستخدم' });
  }
});

// إضافة مستخدم جديد
app.post('/api/users', async (req, res) => {
  try {
    const { username, loginName, password, deviceId, device1, permissions, isActive } = req.body;

    console.log(`➕ Creating new user:`, { username, loginName });

    const bcrypt = require('bcrypt');
    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = await prisma.user.create({
      data: {
        username,
        loginName,
        password: hashedPassword,
        deviceId,
        device1,
        permissions,
        isActive: isActive !== undefined ? isActive : true
      },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        device1: true,
        permissions: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    });

    console.log(`✅ User created successfully with ID: ${newUser.id}`);
    res.json(newUser);
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ error: 'فشل في إنشاء المستخدم' });
  }
});

// تحديث كلمة مرور المستخدم
app.put('/api/users/:id/password', async (req, res) => {
  try {
    const { id } = req.params;
    const { currentPassword, newPassword } = req.body;

    console.log(`🔐 Updating password for user ${id}`);

    // جلب المستخدم الحالي
    const user = await prisma.user.findUnique({
      where: { id: parseInt(id) }
    });

    if (!user) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }

    // التحقق من كلمة المرور الحالية
    const bcrypt = require('bcrypt');
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);

    if (!isCurrentPasswordValid) {
      return res.status(400).json({ error: 'كلمة المرور الحالية غير صحيحة' });
    }

    // تشفير كلمة المرور الجديدة
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // تحديث كلمة المرور
    await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        password: hashedNewPassword,
        updatedAt: new Date()
      }
    });

    console.log(`✅ Password updated successfully for user ${id}`);
    res.json({ message: 'تم تحديث كلمة المرور بنجاح' });
  } catch (error) {
    console.error('Update password error:', error);
    res.status(500).json({ error: 'فشل في تحديث كلمة المرور' });
  }
});

// تحديث معرف جهاز2 للمستخدم
app.put('/api/users/:id/device', async (req, res) => {
  try {
    const { id } = req.params;
    const { device1 } = req.body;

    console.log(`📱 Updating device1 for user ${id}:`, device1);

    // جلب المستخدم الحالي
    const user = await prisma.user.findUnique({
      where: { id: parseInt(id) }
    });

    if (!user) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }

    // تحديث معرف الجهاز2
    const updatedUser = await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        device1: device1 || null,
        updatedAt: new Date()
      }
    });

    console.log(`✅ Device1 updated successfully for user ${id}`);
    res.json({
      message: 'تم تحديث معرف الجهاز2 بنجاح',
      user: {
        id: updatedUser.id,
        device1: updatedUser.device1
      }
    });
  } catch (error) {
    console.error('Update device1 error:', error);
    res.status(500).json({ error: 'فشل في تحديث معرف الجهاز2' });
  }
});

// حذف مستخدم
app.delete('/api/users/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`🗑️ Deleting user ${id}`);

    await prisma.user.delete({
      where: { id: parseInt(id) }
    });

    console.log(`✅ User ${id} deleted successfully`);
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ error: 'فشل في حذف المستخدم' });
  }
});

// Clients API - محمي
app.get('/api/clients', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', status, userId } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    // تصفية العملاء حسب المستخدم والصلاحيات
    if (userId) {
      const currentUser = await prisma.user.findUnique({
        where: { id: parseInt(userId) }
      });

      // التحقق من صلاحيات المدير الكامل (hash8080 فقط)
      const isSystemAdmin = currentUser?.loginName === 'hash8080' ||
        (currentUser?.permissions?.isAdmin === true &&
         currentUser?.permissions?.users?.read === true &&
         currentUser?.permissions?.users?.create === true &&
         currentUser?.permissions?.users?.delete === true);

      console.log(`🔍 User ${userId} (${currentUser?.loginName}) permissions check:`, {
        loginName: currentUser?.loginName,
        isAdmin: currentUser?.permissions?.isAdmin,
        isSystemAdmin: isSystemAdmin,
        permissions: currentUser?.permissions
      });

      // hash8080 يرى جميع العملاء، باقي المستخدمين يرون عملاءهم فقط
      if (!isSystemAdmin) {
        where.userId = parseInt(userId);
        console.log(`🔒 Filtering clients for user ${userId} (${currentUser?.loginName}) - showing own clients only`);
      } else {
        console.log(`👑 User ${userId} (${currentUser?.loginName}) is system admin - showing all clients`);
      }
    }

    if (search) {
      where.OR = [
        { clientName: { contains: search, mode: 'insensitive' } },
        { appName: { contains: search, mode: 'insensitive' } },
        { cardNumber: { contains: search } },
        { clientCode: { equals: parseInt(search) || 0 } }
      ];
    }

    if (status) {
      where.status = parseInt(status);
    }

    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          user: { select: { id: true, username: true, loginName: true } }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.client.count({ where })
    ]);

    console.log(`✅ Returning ${clients.length} clients out of ${total} total for user ${userId}`);
    res.json({
      data: clients,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Clients error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// إضافة عميل جديد
app.post('/api/clients', async (req, res) => {
  try {
    const { clientName, appName, cardNumber, password, ipAddress, status, userId } = req.body;

    console.log(`➕ Creating new client:`, { clientName, appName, userId });

    const bcrypt = require('bcrypt');
    const hashedPassword = await bcrypt.hash(password, 10);

    // توليد رمز العميل تلقائياً
    const lastClient = await prisma.client.findFirst({
      orderBy: { clientCode: 'desc' }
    });
    const nextClientCode = (lastClient?.clientCode || 0) + 1;

    // توليد التوكن المشفر
    const generateToken = () => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let token = '';
      for (let i = 0; i < 12; i++) {
        token += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return token;
    };

    const newClient = await prisma.client.create({
      data: {
        clientName,
        appName,
        cardNumber,
        password: hashedPassword,
        ipAddress,
        status: status || 1,
        clientCode: nextClientCode,
        token: generateToken(),
        userId: userId ? parseInt(userId) : null
      },
      include: {
        user: { select: { id: true, username: true, loginName: true } }
      }
    });

    console.log(`✅ Client created successfully with ID: ${newClient.id}, userId: ${newClient.userId}`);
    res.json(newClient);
  } catch (error) {
    console.error('Create client error:', error);
    res.status(500).json({ error: 'فشل في إنشاء العميل' });
  }
});

// تحديث عميل
app.put('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { clientName, appName, cardNumber, password, ipAddress, status } = req.body;

    console.log(`🔄 Updating client ${id}:`, { clientName, appName });

    const updateData = {
      clientName,
      appName,
      cardNumber,
      ipAddress,
      status,
      updatedAt: new Date()
    };

    // تشفير كلمة المرور إذا تم تغييرها
    if (password) {
      const bcrypt = require('bcrypt');
      updateData.password = await bcrypt.hash(password, 10);
    }

    const updatedClient = await prisma.client.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        user: { select: { id: true, username: true, loginName: true } }
      }
    });

    console.log(`✅ Client ${id} updated successfully`);
    res.json(updatedClient);
  } catch (error) {
    console.error('Update client error:', error);
    res.status(500).json({ error: 'فشل في تحديث العميل' });
  }
});

// حذف عميل
app.delete('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`🗑️ Deleting client ${id}`);

    await prisma.client.delete({
      where: { id: parseInt(id) }
    });

    console.log(`✅ Client ${id} deleted successfully`);
    res.json({ message: 'تم حذف العميل بنجاح' });
  } catch (error) {
    console.error('Delete client error:', error);
    res.status(500).json({ error: 'فشل في حذف العميل' });
  }
});

// Agents API - محمي
app.get('/api/agents', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', agencyType } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (search) {
      where.OR = [
        { agentName: { contains: search, mode: 'insensitive' } },
        { agencyName: { contains: search, mode: 'insensitive' } },
        { loginName: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (agencyType) {
      where.agencyType = agencyType;
    }

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        orderBy: { createdAt: 'desc' }
      }),
      prisma.agent.count({ where })
    ]);

    console.log(`✅ Returning ${agents.length} agents out of ${total} total`);
    res.json({
      data: agents,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Agents error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// Data Records API - محمي
app.get('/api/data-records', apiLimiter, authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', agentId, clientId, status } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (search) {
      where.OR = [
        { agentReference: { equals: parseInt(search) || 0 } },
        { agent: { agentName: { contains: search, mode: 'insensitive' } } },
        { client: { clientName: { contains: search, mode: 'insensitive' } } }
      ];
    }

    if (agentId) {
      where.agentId = parseInt(agentId);
    }

    if (clientId) {
      where.clientId = parseInt(clientId);
    }

    if (status !== undefined) {
      where.operationStatus = parseInt(status);
    }

    const [dataRecords, total] = await Promise.all([
      prisma.dataRecord.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          agent: { select: { agentName: true } },
          client: { select: { clientName: true } }
        },
        orderBy: { operationDate: 'desc' }
      }),
      prisma.dataRecord.count({ where })
    ]);

    console.log(`✅ Returning ${dataRecords.length} data records out of ${total} total`);
    res.json({
      data: dataRecords,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Data records error:', error);
    res.json({ data: [], total: 0, page: 1, totalPages: 0 });
  }
});

// Dashboard Stats - إحصائيات شاملة
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    const [totalUsers, totalClients, totalAgents, totalDataRecords, totalSecurity] = await Promise.all([
      prisma.user.count(),
      prisma.client.count(),
      prisma.agent.count(),
      prisma.dataRecord.count(),
      prisma.loginAttempt.count()
    ]);

    const stats = {
      totalUsers,
      totalClients,
      totalAgents,
      totalDataRecords,
      totalSecurity,
      systemHealth: 'excellent'
    };
    console.log('✅ Dashboard stats:', stats);
    res.json(stats);
  } catch (error) {
    console.error('Dashboard error:', error);
    res.json({
      totalUsers: 0,
      totalClients: 0,
      totalAgents: 0,
      totalDataRecords: 0,
      totalSecurity: 0
    });
  }
});

// Dashboard Recent Activity
app.get('/api/dashboard/recent-activity', async (req, res) => {
  try {
    const recentClients = await prisma.client.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: { user: { select: { username: true } } }
    });

    const activities = recentClients.map(client => ({
      id: `client_${client.id}`,
      type: 'client_created',
      user: client.user?.username || 'مجهول',
      description: `إضافة عميل: ${client.clientName}`,
      timestamp: client.createdAt.toISOString()
    }));

    console.log(`✅ Recent activities: ${activities.length}`);
    res.json({ activities, total: activities.length });
  } catch (error) {
    console.error('Recent activity error:', error);
    res.json({ activities: [], total: 0 });
  }
});

// Security Stats - إحصائيات الأمان الحقيقية
app.get('/api/security/stats', async (req, res) => {
  try {
    const [
      totalAttempts,
      successfulAttempts,
      failedAttempts,
      todayAttempts,
      uniqueIPs
    ] = await Promise.all([
      prisma.loginAttempt.count(),
      prisma.loginAttempt.count({ where: { success: true } }),
      prisma.loginAttempt.count({ where: { success: false } }),
      prisma.loginAttempt.count({
        where: {
          timestamp: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
      prisma.loginAttempt.groupBy({
        by: ['ipAddress'],
        _count: { ipAddress: true }
      })
    ]);

    const stats = {
      totalAttempts,
      successfulLogins: successfulAttempts,
      failedAttempts,
      todayAttempts,
      uniqueIPs: uniqueIPs.length,
      successRate: totalAttempts > 0 ? ((successfulAttempts / totalAttempts) * 100).toFixed(1) : 0,
      suspiciousActivity: failedAttempts > 10 ? Math.floor(failedAttempts / 10) : 0
    };

    console.log('✅ Security stats:', stats);
    res.json(stats);
  } catch (error) {
    console.error('Security stats error:', error);
    res.json({
      totalAttempts: 0,
      successfulLogins: 0,
      failedAttempts: 0,
      todayAttempts: 0,
      uniqueIPs: 0,
      successRate: 0,
      suspiciousActivity: 0
    });
  }
});

// Security Advanced Stats
app.get('/api/security/advanced-stats', async (req, res) => {
  try {
    const totalUsers = await prisma.user.count();
    res.json({
      suspiciousIPs: 0,
      blockedIPs: 0,
      totalLoginAttempts: totalUsers * 2,
      recentAttacks: [],
      lastSecurityScan: new Date().toISOString(),
      systemStatus: 'operational'
    });
  } catch (error) {
    res.json({ suspiciousIPs: 0, blockedIPs: 0, totalLoginAttempts: 0, recentAttacks: [] });
  }
});

// Security Login Attempts - محاولات الدخول الحقيقية
app.get('/api/security/login-attempts', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', success, userType } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (search) {
      where.OR = [
        { ipAddress: { contains: search } },
        { deviceId: { contains: search } },
        { userId: { equals: parseInt(search) || 0 } }
      ];
    }

    if (success !== undefined && success !== '') {
      where.success = success === 'true';
    }

    if (userType) {
      where.userType = userType;
    }

    const [attempts, total] = await Promise.all([
      prisma.loginAttempt.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          user: {
            select: { username: true, loginName: true }
          },
          agent: {
            select: { agentName: true }
          }
        },
        orderBy: { timestamp: 'desc' }
      }),
      prisma.loginAttempt.count({ where })
    ]);


    // تنسيق البيانات للعرض
    const formattedAttempts = attempts.map(attempt => ({
      id: attempt.id.toString(),
      type: attempt.success ? 'success' : 'failed',
      username: attempt.user?.username || attempt.user?.loginName || `User ${attempt.userId}`,
      ip: attempt.ipAddress,
      timestamp: attempt.timestamp.toISOString(),
      userAgent: 'System Login',
      deviceId: attempt.deviceId || 'unknown',
      reason: attempt.success ? null : 'Authentication failed',
      userType: attempt.userType,
      agentName: attempt.agent?.agentName
    }));

    console.log(`✅ Returning ${formattedAttempts.length} login attempts out of ${total} total`);
    res.json({
      attempts: formattedAttempts,
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Login attempts error:', error);
    res.json({ attempts: [], total: 0, page: 1, limit: 10, totalPages: 0 });
  }
});

// ========================================
// APIs المطورين (External APIs)
// ========================================

// API للتحقق المباشر (للمطورين)
app.post('/api/external/verify-direct', async (req, res) => {
  try {
    const { client_code, password } = req.body;

    console.log('🔍 Direct verification request:', { client_code });

    if (!client_code || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: client_code and password',
        error_code: 'VALIDATION_ERROR'
      });
    }

    const client = await prisma.client.findUnique({
      where: { clientCode: parseInt(client_code) }
    });

    if (!client) {
      return res.status(404).json({
        status: 'client_not_found',
        message: 'Client not found'
      });
    }

    if (client.status !== 1) {
      return res.status(403).json({
        status: 'client_inactive',
        message: 'Client account is inactive'
      });
    }

    const passwordValid = await bcrypt.compare(password, client.password);
    if (!passwordValid) {
      return res.status(401).json({
        status: 'invalid_credentials',
        message: 'Invalid password'
      });
    }

    res.json({
      status: 'success',
      message: 'Client verified successfully',
      data: {
        client_code: client.clientCode,
        client_name: client.clientName,
        app_name: client.appName,
        status: client.status,
        token: client.token
      }
    });

  } catch (error) {
    console.error('Direct verification error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Verification service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// API لمصادقة الوكيل (للمطورين)
app.post('/api/external/agent/auth', async (req, res) => {
  try {
    const { login_name, login_password } = req.body;

    console.log('🔐 Agent auth request:', { login_name });

    if (!login_name || !login_password) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: login_name and login_password',
        error_code: 'VALIDATION_ERROR'
      });
    }

    const agent = await prisma.agent.findFirst({
      where: {
        loginName: login_name,
        isActive: true
      }
    });

    if (!agent) {
      return res.status(401).json({
        status: 'agent_error',
        message: 'Agent not found or inactive'
      });
    }

    const passwordValid = await bcrypt.compare(login_password, agent.loginPassword);
    if (!passwordValid) {
      return res.status(401).json({
        status: 'agent_error',
        message: 'Invalid credentials'
      });
    }

    res.json({
      status: 'success',
      message: 'Agent authenticated successfully',
      data: {
        agent_id: agent.id,
        agent_name: agent.agentName,
        agency_name: agent.agencyName,
        agency_type: agent.agencyType,
        token: 'temp_token_' + Date.now()
      }
    });

  } catch (error) {
    console.error('Agent authentication error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Authentication service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// API لإحصائيات سريعة (للمطورين)
app.get('/api/external/stats', async (req, res) => {
  try {
    const [totalClients, activeClients, totalAgents, activeAgents] = await Promise.all([
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.agent.count(),
      prisma.agent.count({ where: { isActive: true } })
    ]);

    res.json({
      status: 'success',
      data: {
        total_clients: totalClients,
        active_clients: activeClients,
        blocked_clients: totalClients - activeClients,
        total_agents: totalAgents,
        active_agents: activeAgents,
        inactive_agents: totalAgents - activeAgents,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Stats error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Stats service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// API لفحص حالة النظام (للمطورين)
app.get('/api/external/health', async (req, res) => {
  try {
    await prisma.$connect();
    const userCount = await prisma.user.count();

    res.json({
      status: 'success',
      message: 'System is healthy',
      data: {
        database: 'connected',
        users: userCount,
        server: 'operational',
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'System health check failed',
      error_code: 'HEALTH_CHECK_ERROR'
    });
  }
});

// Static files
app.use(express.static(path.join(__dirname, '../client/dist')));

// React app fallback
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API not found لاتحاول' });
  }
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Working Server running on http://0.0.0.0:${PORT}`);
  console.log(`🌐 External: http://***********:${PORT}`);
  console.log(`🏠 Local: http://localhost:${PORT}`);
});

console.log('🎯 Working Server initialized');
