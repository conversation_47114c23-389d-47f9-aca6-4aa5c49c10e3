package models

import (
	"time"
	"gorm.io/gorm"
)

// User نموذج المستخدم
type User struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Username    string         `json:"username" gorm:"size:100;not null"`
	LoginName   string         `json:"loginName" gorm:"size:50;uniqueIndex;not null"`
	Password    string         `json:"-" gorm:"size:255;not null"` // مخفي في JSON
	DeviceID    *string        `json:"deviceId" gorm:"size:255"`
	Device1     *string        `json:"device1" gorm:"size:255"`
	Permissions *string        `json:"permissions" gorm:"type:jsonb"`
	IsActive    bool           `json:"isActive" gorm:"default:true"`
	IPAddress   *string        `json:"ipAddress" gorm:"size:45"`
	CreatedAt   time.Time      `json:"createdAt"`
	UpdatedAt   time.Time      `json:"updatedAt"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// العلاقات
	Clients []Client `json:"clients,omitempty" gorm:"foreignKey:UserID"`
}

// Client نموذج العميل
type Client struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	ClientName string         `json:"clientName" gorm:"size:100;not null"`
	AppName    string         `json:"appName" gorm:"size:100;not null"`
	CardNumber string         `json:"cardNumber" gorm:"size:50"`
	ClientCode int            `json:"clientCode" gorm:"uniqueIndex;not null"`
	Password   string         `json:"-" gorm:"size:255;not null"` // مخفي في JSON
	Token      string         `json:"token" gorm:"size:255"`
	IPAddress  string         `json:"ipAddress" gorm:"size:45"`
	Status     int            `json:"status" gorm:"default:1"`
	DataID     *uint          `json:"dataId"`
	UserID     *uint          `json:"userId"`
	CreatedAt  time.Time      `json:"createdAt"`
	UpdatedAt  time.Time      `json:"updatedAt"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
	
	// العلاقات
	User        *User        `json:"user,omitempty" gorm:"foreignKey:UserID"`
	DataRecords []DataRecord `json:"dataRecords,omitempty" gorm:"foreignKey:ClientID"`
}

// Agent نموذج الوكيل
type Agent struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	AgentName     string         `json:"agentName" gorm:"size:100;not null"`
	AgencyName    string         `json:"agencyName" gorm:"size:100;not null"`
	AgencyType    string         `json:"agencyType" gorm:"size:100;not null"`
	IPAddress     string         `json:"ipAddress" gorm:"size:45;not null"`
	LoginName     *string        `json:"loginName" gorm:"size:50;uniqueIndex"`
	LoginPassword *string        `json:"-" gorm:"size:255"` // مخفي في JSON
	DeviceID      *string        `json:"deviceId" gorm:"size:255"`
	IsActive      bool           `json:"isActive" gorm:"default:true"`
	CreatedAt     time.Time      `json:"createdAt"`
	UpdatedAt     time.Time      `json:"updatedAt"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`
	
	// العلاقات
	DataRecords []DataRecord `json:"dataRecords,omitempty" gorm:"foreignKey:AgentID"`
}

// DataRecord نموذج سجل البيانات
type DataRecord struct {
	ID                uint           `json:"id" gorm:"primaryKey"`
	AgentID           uint           `json:"agentId" gorm:"not null"`
	ClientID          uint           `json:"clientId" gorm:"not null"`
	ClientCode        int            `json:"clientCode" gorm:"not null"`
	OperationStatus   int            `json:"operationStatus" gorm:"default:1"`
	OperationDate     time.Time      `json:"operationDate" gorm:"not null"`
	AgentReference    int            `json:"agentReference"`
	ClientIPAddress   string         `json:"clientIpAddress" gorm:"size:45"`
	CreatedAt         time.Time      `json:"createdAt"`
	UpdatedAt         time.Time      `json:"updatedAt"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`
	
	// العلاقات
	Agent  *Agent  `json:"agent,omitempty" gorm:"foreignKey:AgentID"`
	Client *Client `json:"client,omitempty" gorm:"foreignKey:ClientID"`
}

// LoginAttempt نموذج محاولات تسجيل الدخول
type LoginAttempt struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserType  string         `json:"userType" gorm:"size:20;not null"` // user, client, agent
	UserID    *uint          `json:"userId"`
	ClientID  *uint          `json:"clientId"`
	AgentID   *uint          `json:"agentId"`
	DeviceID  string         `json:"deviceId" gorm:"size:255;not null"`
	IPAddress string         `json:"ipAddress" gorm:"size:45;not null"`
	Success   bool           `json:"success" gorm:"default:false"`
	CreatedAt time.Time      `json:"createdAt"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// SecurityLog نموذج سجل الأمان
type SecurityLog struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	EventType string         `json:"eventType" gorm:"size:50;not null"`
	Details   string         `json:"details" gorm:"type:text"`
	IPAddress string         `json:"ipAddress" gorm:"size:45;not null"`
	UserAgent string         `json:"userAgent" gorm:"type:text"`
	UserID    *uint          `json:"userId"`
	Severity  string         `json:"severity" gorm:"size:20;default:'MEDIUM'"`
	CreatedAt time.Time      `json:"createdAt"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// APIResponse هيكل الاستجابة المعياري
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Code    string      `json:"code,omitempty"`
}

// PaginatedResponse هيكل الاستجابة مع التصفح
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int         `json:"totalPages"`
}

// LoginRequest طلب تسجيل الدخول
type LoginRequest struct {
	LoginName string `json:"loginName" binding:"required"`
	Password  string `json:"password" binding:"required"`
	DeviceID  string `json:"deviceId" binding:"required"`
}

// ClientLoginRequest طلب تسجيل دخول العميل
type ClientLoginRequest struct {
	ClientCode int    `json:"clientCode" binding:"required"`
	Password   string `json:"password" binding:"required"`
}

// AgentAuthRequest طلب مصادقة الوكيل
type AgentAuthRequest struct {
	LoginName     string `json:"login_name" binding:"required"`
	LoginPassword string `json:"login_password" binding:"required"`
}

// VerifyDirectRequest طلب التحقق المباشر
type VerifyDirectRequest struct {
	ClientCode int    `json:"client_code" binding:"required"`
	Password   string `json:"password" binding:"required"`
}

// JWTClaims مطالبات JWT
type JWTClaims struct {
	UserID    uint   `json:"userId"`
	LoginName string `json:"loginName"`
	DeviceID  string `json:"deviceId"`
	Type      string `json:"type"` // user, client, agent
}

// TableName تحديد أسماء الجداول
func (User) TableName() string        { return "users" }
func (Client) TableName() string      { return "clients" }
func (Agent) TableName() string       { return "agents" }
func (DataRecord) TableName() string  { return "data_records" }
func (LoginAttempt) TableName() string { return "login_attempts" }
func (SecurityLog) TableName() string { return "security_logs" }
