package main

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

func main() {
	log.Println("🚀 ========================================")
	log.Println("🏢 Yemen Client Management System - Golang Test")
	log.Println("🚀 ========================================")

	// Create Gin router
	router := gin.Default()

	// Simple health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "OK",
			"message":   "Golang server is working!",
			"server":    "Golang + Gin",
			"version":   "1.0.0",
		})
	})

	// API test
	router.GET("/api/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "API يعمل بشكل صحيح - Golang",
			"server":  "Yemen Client Management System - Golang",
			"status":  "operational",
		})
	})

	// Root endpoint
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "🎉 خادم Golang يعمل بنجاح!",
			"status":  "ready",
		})
	})

	port := "8080"
	log.Printf("📡 Server starting on port: %s", port)
	log.Printf("🌐 Local access: http://localhost:%s", port)
	log.Printf("🌐 External access: http://***********:%s", port)
	log.Println("✅ Server ready for connections!")
	log.Println("🚀 ========================================")

	// Start server on all interfaces
	if err := router.Run("0.0.0.0:" + port); err != nil {
		log.Fatal("❌ Failed to start server:", err)
	}
}
