package routes

import (
	"encoding/json"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UsersRoutes - مطابق تماماً لـ server/routes/users.js
func UsersRoutes(router *gin.RouterGroup, db *gorm.DB) {
	// الحصول على جميع المستخدمين - مطابق لـ users.js
	router.GET("/", func(c *gin.Context) {
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
		search := c.<PERSON>("search", "")
		offset := (page - 1) * limit

		if db == nil {
			// Mock data when no database
			c.JSON(http.StatusOK, gin.H{
				"success":    true,
				"data":       []gin.H{},
				"total":      0,
				"page":       page,
				"totalPages": 0,
			})
			return
		}

		// Build where clause
		query := db.Table("users")
		if search != "" {
			query = query.Where("username ILIKE ? OR login_name ILIKE ?", "%"+search+"%", "%"+search+"%")
		}

		// Get total count
		var total int64
		query.Count(&total)

		// Get users with pagination
		var users []map[string]interface{}
		query.Select("id, username, login_name, device_id, device1, permissions, is_active, created_at, updated_at").
			Offset(offset).Limit(limit).
			Order("created_at DESC").
			Find(&users)

		// Add client count for each user
		for i := range users {
			var clientCount int64
			if userID, ok := users[i]["id"].(uint); ok {
				db.Table("clients").Where("user_id = ?", userID).Count(&clientCount)
				users[i]["_count"] = map[string]int64{"clients": clientCount}
			}
		}

		totalPages := int((total + int64(limit) - 1) / int64(limit))

		c.JSON(http.StatusOK, gin.H{
			"success":    true,
			"data":       users,
			"total":      total,
			"page":       page,
			"totalPages": totalPages,
		})
	})

	// إضافة مستخدم جديد - مطابق لـ users.js
	router.POST("/", func(c *gin.Context) {
		var userData struct {
			Username    string                 `json:"username" binding:"required"`
			LoginName   string                 `json:"loginName" binding:"required,min=8"`
			Password    string                 `json:"password" binding:"required,min=8"`
			Permissions map[string]interface{} `json:"permissions" binding:"required"`
			DeviceID    *string                `json:"deviceId"`
		}

		if err := c.ShouldBindJSON(&userData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"errors":  []string{err.Error()},
			})
			return
		}

		if db == nil {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "تم إنشاء المستخدم بنجاح (mock)",
				"user": gin.H{
					"id":        1,
					"username":  userData.Username,
					"loginName": userData.LoginName,
				},
			})
			return
		}

		// التحقق من عدم تكرار اسم المستخدم
		var existingUser struct {
			ID uint `json:"id"`
		}
		err := db.Table("users").Where("username = ? OR login_name = ?", userData.Username, userData.LoginName).First(&existingUser).Error
		if err == nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "اسم المستخدم أو اسم تسجيل الدخول موجود بالفعل",
			})
			return
		}

		// تشفير كلمة المرور
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(userData.Password), 12)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "خطأ في تشفير كلمة المرور",
			})
			return
		}

		// تحويل permissions إلى JSON
		permissionsJSON, _ := json.Marshal(userData.Permissions)

		// إنشاء المستخدم
		newUser := map[string]interface{}{
			"username":    userData.Username,
			"login_name":  userData.LoginName,
			"password":    string(hashedPassword),
			"permissions": string(permissionsJSON),
			"device_id":   userData.DeviceID,
			"is_active":   true,
			"created_at":  time.Now(),
			"updated_at":  time.Now(),
		}

		result := db.Table("users").Create(&newUser)
		if result.Error != nil {
			log.Printf("❌ Create user error: %v", result.Error)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "خطأ في إنشاء المستخدم",
			})
			return
		}

		log.Printf("✅ User created successfully: %s", userData.Username)

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "تم إنشاء المستخدم بنجاح",
			"user": gin.H{
				"id":          newUser["id"],
				"username":    userData.Username,
				"loginName":   userData.LoginName,
				"deviceId":    userData.DeviceID,
				"permissions": userData.Permissions,
				"isActive":    true,
				"createdAt":   newUser["created_at"],
			},
		})
	})

	// تحديث مستخدم - مطابق لـ users.js
	router.PUT("/:id", func(c *gin.Context) {
		userID, err := strconv.Atoi(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "معرف المستخدم غير صحيح",
			})
			return
		}

		var updateData map[string]interface{}
		if err := c.ShouldBindJSON(&updateData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		if db == nil {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "تم تحديث المستخدم بنجاح (mock)",
			})
			return
		}

		// التحقق من وجود المستخدم
		var existingUser struct {
			ID uint `json:"id"`
		}
		err = db.Table("users").Where("id = ?", userID).First(&existingUser).Error
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "المستخدم غير موجود",
			})
			return
		}

		// تحضير بيانات التحديث
		updateFields := map[string]interface{}{
			"updated_at": time.Now(),
		}

		if username, ok := updateData["username"]; ok {
			updateFields["username"] = username
		}
		if loginName, ok := updateData["loginName"]; ok {
			updateFields["login_name"] = loginName
		}
		if password, ok := updateData["password"]; ok && password != "" {
			hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password.(string)), 12)
			if err == nil {
				updateFields["password"] = string(hashedPassword)
			}
		}
		if permissions, ok := updateData["permissions"]; ok {
			permissionsJSON, _ := json.Marshal(permissions)
			updateFields["permissions"] = string(permissionsJSON)
		}
		if deviceID, ok := updateData["deviceId"]; ok {
			updateFields["device_id"] = deviceID
		}
		if isActive, ok := updateData["isActive"]; ok {
			updateFields["is_active"] = isActive
		}

		// تحديث المستخدم
		result := db.Table("users").Where("id = ?", userID).Updates(updateFields)
		if result.Error != nil {
			log.Printf("❌ Update user error: %v", result.Error)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "خطأ في تحديث المستخدم",
			})
			return
		}

		log.Printf("✅ User updated successfully: ID %d", userID)

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "تم تحديث المستخدم بنجاح",
		})
	})

	// حذف مستخدم - مطابق لـ users.js
	router.DELETE("/:id", func(c *gin.Context) {
		userID, err := strconv.Atoi(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "معرف المستخدم غير صحيح",
			})
			return
		}

		if db == nil {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "تم حذف المستخدم بنجاح (mock)",
			})
			return
		}

		// حذف المستخدم
		result := db.Table("users").Where("id = ?", userID).Delete(nil)
		if result.Error != nil {
			log.Printf("❌ Delete user error: %v", result.Error)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "خطأ في حذف المستخدم",
			})
			return
		}

		if result.RowsAffected == 0 {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "المستخدم غير موجود",
			})
			return
		}

		log.Printf("✅ User deleted successfully: ID %d", userID)

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "تم حذف المستخدم بنجاح",
		})
	})

	// تحديث معرف الجهاز - مطابق لـ users.js
	router.PUT("/:id/device", func(c *gin.Context) {
		userID, err := strconv.Atoi(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "معرف المستخدم غير صحيح",
			})
			return
		}

		var deviceData struct {
			DeviceID string `json:"deviceId"`
		}
		if err := c.ShouldBindJSON(&deviceData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		if db == nil {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "تم تحديث معرف الجهاز بنجاح (mock)",
			})
			return
		}

		// تحديث معرف الجهاز
		result := db.Table("users").Where("id = ?", userID).Updates(map[string]interface{}{
			"device_id":  deviceData.DeviceID,
			"updated_at": time.Now(),
		})

		if result.Error != nil {
			log.Printf("❌ Update device error: %v", result.Error)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "خطأ في تحديث معرف الجهاز",
			})
			return
		}

		log.Printf("✅ Device updated successfully for user ID %d", userID)

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "تم تحديث معرف الجهاز بنجاح",
		})
	})
}
