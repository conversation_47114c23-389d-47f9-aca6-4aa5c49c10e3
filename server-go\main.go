package main

import (
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Database instance
var db *gorm.DB

// Models - مطابقة لقاعدة البيانات الحالية
type User struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Username    string    `json:"username" gorm:"unique;not null"`
	LoginName   string    `json:"loginName" gorm:"unique;not null"`
	Password    string    `json:"-" gorm:"not null"` // مخفي في JSON
	DeviceID    *string   `json:"deviceId"`
	Device1     *string   `json:"device1"`
	Permissions *string   `json:"permissions"`
	IsActive    bool      `json:"isActive" gorm:"default:true"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

type Client struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	ClientCode int       `json:"clientCode" gorm:"unique;not null"`
	ClientName string    `json:"clientName" gorm:"not null"`
	Password   string    `json:"-" gorm:"not null"`
	Token      *string   `json:"token"`
	AppName    *string   `json:"appName"`
	IPAddress  *string   `json:"ipAddress"`
	Status     int       `json:"status" gorm:"default:1"`
	UserID     *uint     `json:"userId"`
	User       *User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

type Agent struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	AgentCode  int       `json:"agentCode" gorm:"unique;not null"`
	AgentName  string    `json:"agentName" gorm:"not null"`
	AgencyName *string   `json:"agencyName"`
	AgencyType *string   `json:"agencyType"`
	IPAddress  *string   `json:"ipAddress"`
	LoginName  *string   `json:"loginName"`
	IsActive   bool      `json:"isActive" gorm:"default:true"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

type DataRecord struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	ClientID        uint      `json:"clientId" gorm:"not null"`
	AgentID         uint      `json:"agentId" gorm:"not null"`
	ClientCode      int       `json:"clientCode"`
	AgentCode       int       `json:"agentCode"`
	OperationType   string    `json:"operationType"`
	OperationStatus int       `json:"operationStatus" gorm:"default:1"`
	OperationDate   time.Time `json:"operationDate"`
	IPAddress       *string   `json:"ipAddress"`
	Client          *Client   `json:"client,omitempty" gorm:"foreignKey:ClientID"`
	Agent           *Agent    `json:"agent,omitempty" gorm:"foreignKey:AgentID"`
	CreatedAt       time.Time `json:"createdAt"`
	UpdatedAt       time.Time `json:"updatedAt"`
}

// Response structures
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

type PaginatedResponse struct {
	Success    bool        `json:"success"`
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	TotalPages int         `json:"totalPages"`
}

type HealthResponse struct {
	Status    string                 `json:"status"`
	Timestamp string                 `json:"timestamp"`
	Database  map[string]interface{} `json:"database"`
	Server    map[string]interface{} `json:"server"`
}

// Initialize database
func initDatabase() {
	// Load environment variables
	err := godotenv.Load("../.env")
	if err != nil {
		log.Println("Warning: .env file not found, using system environment variables")
	}

	// Get database URL from environment
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		// استخدام نفس إعدادات قاعدة البيانات من .env
		dsn = "postgresql://postgres:yemen123@localhost:5432/yemclient_db?sslmode=disable"
	}

	log.Printf("🔗 Connecting to database: %s", dsn)

	// Connect to database
	db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("❌ Failed to connect to database:", err)
	}

	log.Println("✅ Database connected successfully")

	// Auto-migrate tables (مطابقة للهيكل الحالي)
	err = db.AutoMigrate(&User{}, &Client{}, &Agent{}, &DataRecord{})
	if err != nil {
		log.Fatal("❌ Failed to migrate database:", err)
	}

	log.Println("✅ Database migration completed")

	// Print counts
	var userCount, clientCount, agentCount int64
	db.Model(&User{}).Count(&userCount)
	db.Model(&Client{}).Count(&clientCount)
	db.Model(&Agent{}).Count(&agentCount)

	log.Printf("👥 Users: %d", userCount)
	log.Printf("🏢 Clients: %d", clientCount)
	log.Printf("🤝 Agents: %d", agentCount)
}

// Setup routes
func setupRoutes() *gin.Engine {
	// Set Gin mode
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()

	// Middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS configuration (مطابق لـ Node.js)
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowCredentials = true
	config.AllowHeaders = []string{"*"}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"}
	router.Use(cors.New(config))

	// Health check endpoint
	router.GET("/health", healthCheck)

	// API test endpoint
	router.GET("/api/test", apiTest)

	// Authentication routes
	auth := router.Group("/api")
	{
		auth.POST("/auth/login", userLogin)
		auth.POST("/user/login", userLogin) // نفس الوظيفة
		auth.POST("/client/login", clientLogin)
	}

	// Protected routes (تحتاج JWT)
	protected := router.Group("/api")
	protected.Use(authMiddleware())
	{
		// Dashboard APIs
		protected.GET("/dashboard/stats", dashboardStats)
		protected.GET("/dashboard/recent-activity", recentActivity)

		// Security APIs
		protected.GET("/security/stats", securityStats)
		protected.GET("/security/login-attempts", loginAttempts)

		// Data APIs
		protected.GET("/users", getUsers)
		protected.GET("/clients", getClients)
		protected.GET("/agents", getAgents)
		protected.GET("/data-records", getDataRecords)
	}

	// External APIs (بدون JWT)
	external := router.Group("/api/external")
	{
		external.POST("/verify-direct", verifyDirect)
		external.GET("/stats", externalStats)
		external.GET("/health", externalHealth)
	}

	// Static files (React build)
	router.Static("/static", "../client/dist/assets")
	router.StaticFile("/", "../client/dist/index.html")
	router.StaticFile("/favicon.ico", "../client/dist/favicon.ico")

	// Catch all routes for React Router
	router.NoRoute(func(c *gin.Context) {
		if c.Request.URL.Path[:4] == "/api" {
			c.JSON(http.StatusNotFound, APIResponse{
				Success: false,
				Error:   "API not found",
			})
			return
		}
		c.File("../client/dist/index.html")
	})

	return router
}

func main() {
	log.Println("🚀 ========================================")
	log.Println("🏢 Yemen Client Management System - Golang")
	log.Println("🚀 ========================================")

	// Initialize database
	initDatabase()

	// Setup routes
	router := setupRoutes()

	// Get port from environment
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("📡 Server starting on port: %s", port)
	log.Printf("🌐 Local access: http://localhost:%s", port)
	log.Printf("🌐 External access: http://***********:%s", port)
	log.Println("✅ Server ready for connections!")
	log.Println("🚀 ========================================")

	// Start server
	if err := router.Run(":" + port); err != nil {
		log.Fatal("❌ Failed to start server:", err)
	}
}
