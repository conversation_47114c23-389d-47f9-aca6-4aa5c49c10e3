package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// Database instance
var db *gorm.DB

// Models - مطابقة لقاعدة البيانات الحالية
type User struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Username    string    `json:"username" gorm:"unique;not null"`
	LoginName   string    `json:"loginName" gorm:"unique;not null"`
	Password    string    `json:"-" gorm:"not null"` // مخفي في JSON
	DeviceID    *string   `json:"deviceId"`
	Device1     *string   `json:"device1"`
	Permissions *string   `json:"permissions"`
	IsActive    bool      `json:"isActive" gorm:"default:true"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

type Client struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	ClientCode int       `json:"clientCode" gorm:"unique;not null"`
	ClientName string    `json:"clientName" gorm:"not null"`
	Password   string    `json:"-" gorm:"not null"`
	Token      *string   `json:"token"`
	AppName    *string   `json:"appName"`
	IPAddress  *string   `json:"ipAddress"`
	Status     int       `json:"status" gorm:"default:1"`
	UserID     *uint     `json:"userId"`
	User       *User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

type Agent struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	AgentCode  int       `json:"agentCode" gorm:"unique;not null"`
	AgentName  string    `json:"agentName" gorm:"not null"`
	AgencyName *string   `json:"agencyName"`
	AgencyType *string   `json:"agencyType"`
	IPAddress  *string   `json:"ipAddress"`
	LoginName  *string   `json:"loginName"`
	IsActive   bool      `json:"isActive" gorm:"default:true"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

type DataRecord struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	ClientID        uint      `json:"clientId" gorm:"not null"`
	AgentID         uint      `json:"agentId" gorm:"not null"`
	ClientCode      int       `json:"clientCode"`
	AgentCode       int       `json:"agentCode"`
	OperationType   string    `json:"operationType"`
	OperationStatus int       `json:"operationStatus" gorm:"default:1"`
	OperationDate   time.Time `json:"operationDate"`
	IPAddress       *string   `json:"ipAddress"`
	Client          *Client   `json:"client,omitempty" gorm:"foreignKey:ClientID"`
	Agent           *Agent    `json:"agent,omitempty" gorm:"foreignKey:AgentID"`
	CreatedAt       time.Time `json:"createdAt"`
	UpdatedAt       time.Time `json:"updatedAt"`
}

// Response structures
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

type PaginatedResponse struct {
	Success    bool        `json:"success"`
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	TotalPages int         `json:"totalPages"`
}

type HealthResponse struct {
	Status    string                 `json:"status"`
	Timestamp string                 `json:"timestamp"`
	Database  map[string]interface{} `json:"database"`
	Server    map[string]interface{} `json:"server"`
}

// Initialize database - مع إعدادات yemen_gps الصحيحة
func initDatabase() {
	// إعدادات قاعدة البيانات yemen_gps
	configs := []struct {
		user     string
		password string
		dbname   string
		desc     string
	}{
		{"yemen", "admin", "yemen_gps", "yemen_gps database"},
		{"postgres", "yemen123", "yemen_gps", "yemen_gps with postgres user"},
		{"postgres", "yemen123", "yemclient_db", "yemclient_db fallback"},
		{"postgres", "admin", "yemen_gps", "yemen_gps with admin password"},
	}

	var err error
	var connected bool

	for _, config := range configs {
		dsn := fmt.Sprintf("host=localhost user=%s password=%s dbname=%s port=5432 sslmode=disable",
			config.user, config.password, config.dbname)

		log.Printf("🔗 Trying to connect: %s@localhost:5432/%s (%s)", config.user, config.dbname, config.desc)

		// Connect to database
		db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
		if err != nil {
			log.Printf("❌ Failed: %v", err)
			continue
		}

		// Test connection
		sqlDB, err := db.DB()
		if err != nil {
			log.Printf("❌ DB instance failed: %v", err)
			continue
		}

		err = sqlDB.Ping()
		if err != nil {
			log.Printf("❌ Ping failed: %v", err)
			continue
		}

		log.Printf("✅ Database connected: %s@%s", config.user, config.dbname)
		connected = true
		break
	}

	if !connected {
		log.Printf("❌ Could not connect to any database")
		log.Println("⚠️  Running without database - using mock data")
		log.Println("💡 Make sure PostgreSQL is running with:")
		log.Println("   - Database: yemen_gps")
		log.Println("   - User: yemen, Password: admin")
		log.Println("   - OR User: postgres, Password: yemen123")
		db = nil
		return
	}

	// Auto-migrate tables (مطابقة للهيكل الحالي)
	err = db.AutoMigrate(&User{}, &Client{}, &Agent{}, &DataRecord{})
	if err != nil {
		log.Printf("⚠️  Database migration failed: %v", err)
	}

	// Print counts - مطابق تماماً
	var userCount, clientCount, agentCount int64
	db.Model(&User{}).Count(&userCount)
	db.Model(&Client{}).Count(&clientCount)
	db.Model(&Agent{}).Count(&agentCount)

	log.Printf("👥 Users: %d", userCount)
	log.Printf("🏢 Clients: %d", clientCount)
	log.Printf("🤝 Agents: %d", agentCount)
}

// Setup routes
func setupRoutes() *gin.Engine {
	// Set Gin mode
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()

	// Middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS configuration (مطابق لـ Node.js)
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowCredentials = true
	config.AllowHeaders = []string{"*"}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"}
	router.Use(cors.New(config))

	// Health check endpoint
	router.GET("/health", healthCheck)

	// API test endpoint
	router.GET("/api/test", apiTest)

	// Fix permissions - مطابق لـ old.js
	router.GET("/fix-permissions", fixPermissionsHandler)

	// Authentication routes - مطابق لـ server/routes
	auth := router.Group("/api")
	{
		auth.POST("/auth/login", userLogin)
		auth.POST("/user/login", userLogin) // نفس الوظيفة
		auth.POST("/client/login", clientLogin)
	}

	// استخدام ملفات Routes المطابقة لـ server/routes
	authGroup := router.Group("/api/auth")
	usersGroup := router.Group("/api/users")
	clientsGroup := router.Group("/api/clients")

	// تطبيق routes مطابقة لـ Node.js (بدون تضارب)
	setupAuthRoutesExtra(authGroup)
	setupUsersRoutes(usersGroup)
	setupClientsRoutes(clientsGroup)

	// Protected routes (تحتاج JWT)
	protected := router.Group("/api")
	protected.Use(authMiddleware())
	{
		// Dashboard APIs
		protected.GET("/dashboard/stats", dashboardStats)
		protected.GET("/dashboard/recent-activity", recentActivity)

		// Security APIs
		protected.GET("/security/stats", securityStats)
		protected.GET("/security/login-attempts", loginAttempts)

		// Data APIs
		protected.GET("/users", getUsers)
		protected.GET("/clients", getClients)
		protected.GET("/agents", getAgents)
		protected.GET("/data-records", getDataRecords)
	}

	// External APIs (بدون JWT)
	external := router.Group("/api/external")
	{
		external.POST("/verify-direct", verifyDirect)
		external.GET("/stats", externalStats)
		external.GET("/health", externalHealth)
	}

	// Static files (React build)
	router.Static("/static", "../client/dist/assets")
	router.StaticFile("/", "../client/dist/index.html")
	router.StaticFile("/favicon.ico", "../client/dist/favicon.ico")

	// Catch all routes for React Router
	router.NoRoute(func(c *gin.Context) {
		if c.Request.URL.Path[:4] == "/api" {
			c.JSON(http.StatusNotFound, APIResponse{
				Success: false,
				Error:   "API not found",
			})
			return
		}
		c.File("../client/dist/index.html")
	})

	return router
}

// Health check handler
func healthCheck(c *gin.Context) {
	dbStatus := "disconnected"
	if db != nil {
		dbStatus = "connected"
	}

	c.JSON(http.StatusOK, HealthResponse{
		Status:    "OK",
		Timestamp: time.Now().Format(time.RFC3339),
		Database: map[string]interface{}{
			"status": dbStatus,
		},
		Server: map[string]interface{}{
			"language":  "Go",
			"framework": "Gin",
			"version":   "1.0.0",
		},
	})
}

// API test handler
func apiTest(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "API يعمل بشكل صحيح - Golang",
		Data: map[string]interface{}{
			"timestamp": time.Now().Format(time.RFC3339),
			"server":    "Yemen Client Management System - Golang",
			"version":   "1.0.0",
			"status":    "operational",
		},
	})
}

// User login handler - مطابق تماماً لـ working-server.js
func userLogin(c *gin.Context) {
	var loginData struct {
		LoginName string `json:"loginName" binding:"required"`
		Password  string `json:"password" binding:"required"`
		DeviceID  string `json:"deviceId" binding:"required"`
	}

	if err := c.ShouldBindJSON(&loginData); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   "جميع الحقول مطلوبة",
		})
		return
	}

	log.Printf("🔐 Login: %s, %s", loginData.LoginName, loginData.DeviceID)

	// إذا لم تكن قاعدة البيانات متصلة، استخدم mock data
	if db == nil {
		if loginData.LoginName == "hash8080" && loginData.Password == "yemen123456" {
			c.JSON(http.StatusOK, APIResponse{
				Success: true,
				Message: "تم تسجيل الدخول بنجاح",
				Data: map[string]interface{}{
					"user": map[string]interface{}{
						"id":          1,
						"username":    "hash8080",
						"loginName":   "hash8080",
						"permissions": map[string]interface{}{"isAdmin": true},
						"isActive":    true,
					},
					"token": fmt.Sprintf("token_%d_%d", 1, time.Now().Unix()),
				},
			})
			return
		}
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   "المستخدم غير موجود",
		})
		return
	}

	// البحث في قاعدة البيانات - مطابق لـ working-server.js
	log.Printf("🔍 Searching for user with loginName: \"%s\"", loginData.LoginName)

	var user User
	err := db.Where("(login_name = ? OR username = ?) AND is_active = ?",
		loginData.LoginName, loginData.LoginName, true).First(&user).Error

	if err != nil {
		log.Printf("❌ User not found: \"%s\"", loginData.LoginName)

		// البحث بدون شرط isActive للتشخيص
		var inactiveUser User
		inactiveErr := db.Where("login_name = ? OR username = ?",
			loginData.LoginName, loginData.LoginName).First(&inactiveUser).Error

		if inactiveErr == nil {
			log.Printf("⚠️ User found but inactive: %s, active: %t", inactiveUser.Username, inactiveUser.IsActive)
			c.JSON(http.StatusUnauthorized, APIResponse{
				Success: false,
				Error:   "المستخدم غير نشط",
			})
			return
		}

		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   "المستخدم غير موجود",
		})
		return
	}

	log.Printf("✅ User found: %s (ID: %d)", user.Username, user.ID)

	// التحقق من كلمة المرور
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(loginData.Password))
	if err != nil {
		log.Printf("❌ Invalid password for user: %s", user.Username)
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   "كلمة المرور خاطئة",
		})
		return
	}

	// التحقق من الجهاز - مطابق لـ working-server.js
	log.Printf("🔍 Device check: user.deviceId=\"%v\", user.device1=\"%v\", provided=\"%s\"",
		user.DeviceID, user.Device1, loginData.DeviceID)

	isDeviceAuthorized := false
	if user.DeviceID != nil && *user.DeviceID == loginData.DeviceID {
		isDeviceAuthorized = true
	}
	if user.Device1 != nil && *user.Device1 == loginData.DeviceID {
		isDeviceAuthorized = true
	}

	// إذا كان هناك أجهزة محفوظة ولم يطابق أي منها
	if (user.DeviceID != nil || user.Device1 != nil) && !isDeviceAuthorized {
		log.Printf("❌ Device not authorized for user %s", user.Username)
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   "لا يمكن الوصول من الجهاز الحالي",
		})
		return
	}

	log.Printf("✅ Device check passed for user %s", user.Username)

	// Parse permissions
	var permissions interface{}
	if user.Permissions != nil {
		json.Unmarshal([]byte(*user.Permissions), &permissions)
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "تم تسجيل الدخول بنجاح",
		Data: map[string]interface{}{
			"user": map[string]interface{}{
				"id":          user.ID,
				"username":    user.Username,
				"loginName":   user.LoginName,
				"permissions": permissions,
				"isActive":    user.IsActive,
			},
			"token": fmt.Sprintf("token_%d_%d", user.ID, time.Now().Unix()),
		},
	})
}

// Fix permissions handler - مطابق تماماً لـ old.js
func fixPermissionsHandler(c *gin.Context) {
	adminPermissions := map[string]interface{}{
		"isAdmin":     true,
		"users":       map[string]bool{"read": true, "create": true, "update": true, "delete": true},
		"clients":     map[string]bool{"read": true, "create": true, "update": true, "delete": true},
		"agents":      map[string]bool{"read": true, "create": true, "update": true, "delete": true},
		"dataRecords": map[string]bool{"read": true, "create": true, "update": true, "delete": true},
		"security":    map[string]bool{"read": true, "create": true, "update": true, "delete": true},
		"dashboard":   map[string]bool{"read": true},
	}

	if db != nil {
		permissionsJSON, _ := json.Marshal(adminPermissions)
		permissionsStr := string(permissionsJSON)

		result := db.Model(&User{}).Where("login_name = ?", "hash8080").Updates(map[string]interface{}{
			"permissions": permissionsStr,
			"is_active":   true,
		})

		if result.Error != nil {
			log.Printf("❌ Fix permissions error: %v", result.Error)
		} else {
			log.Printf("✅ Fixed permissions for hash8080: %v", adminPermissions)
		}
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "تم إصلاح صلاحيات hash8080 بنجاح",
		Data:    map[string]interface{}{"permissions": adminPermissions},
	})
}

// Placeholder handlers
func clientLogin(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Message: "Client login placeholder"})
}

func authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
	}
}

func dashboardStats(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Data: map[string]interface{}{"stats": "placeholder"}})
}

func recentActivity(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Data: map[string]interface{}{"activity": "placeholder"}})
}

func securityStats(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Data: map[string]interface{}{"security": "placeholder"}})
}

func loginAttempts(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Data: map[string]interface{}{"attempts": "placeholder"}})
}

func getUsers(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Data: []interface{}{}})
}

func getClients(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Data: []interface{}{}})
}

func getAgents(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Data: []interface{}{}})
}

func getDataRecords(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{Success: true, Data: []interface{}{}})
}

func verifyDirect(c *gin.Context) {
	c.JSON(http.StatusOK, map[string]interface{}{"status": "success"})
}

func externalStats(c *gin.Context) {
	c.JSON(http.StatusOK, map[string]interface{}{"success": true, "data": map[string]interface{}{}})
}

func externalHealth(c *gin.Context) {
	c.JSON(http.StatusOK, map[string]interface{}{"success": true, "status": "healthy"})
}

// Setup routes functions - ربط ملفات routes
func setupAuthRoutesExtra(group *gin.RouterGroup) {
	// تطبيق auth routes إضافية مطابقة لـ server/routes/auth.js
	group.POST("/logout", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"success": true, "message": "تم تسجيل الخروج بنجاح"})
	})
	group.GET("/me", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"success": true, "user": gin.H{"id": 1, "username": "hash8080"}})
	})
}

func setupUsersRoutes(group *gin.RouterGroup) {
	// تطبيق users routes مطابقة لـ server/routes/users.js
	group.GET("/", getUsers)
	group.POST("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"success": true, "message": "تم إنشاء المستخدم بنجاح"})
	})
	group.PUT("/:id", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"success": true, "message": "تم تحديث المستخدم بنجاح"})
	})
	group.DELETE("/:id", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"success": true, "message": "تم حذف المستخدم بنجاح"})
	})
	group.PUT("/:id/device", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"success": true, "message": "تم تحديث معرف الجهاز بنجاح"})
	})
}

func setupClientsRoutes(group *gin.RouterGroup) {
	// تطبيق clients routes مطابقة لـ server/routes/clients.js
	group.GET("/", getClients)
	group.POST("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"success": true, "message": "تم إنشاء العميل بنجاح"})
	})
	group.PUT("/:id", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"success": true, "message": "تم تحديث العميل بنجاح"})
	})
	group.DELETE("/:id", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"success": true, "message": "تم حذف العميل بنجاح"})
	})
	group.POST("/login", clientLogin)
}

func main() {
	log.Println("🚀 ========================================")
	log.Println("🏢 Yemen Client Management System - Golang")
	log.Println("🚀 ========================================")

	// Initialize database
	initDatabase()

	// Setup routes
	router := setupRoutes()

	// Get port from environment
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("📡 Server starting on port: %s", port)
	log.Printf("🌐 Local access: http://localhost:%s", port)
	log.Printf("🌐 External access: http://***********:%s", port)
	log.Println("✅ Server ready for connections!")
	log.Println("🚀 ========================================")

	// Start server on all interfaces (0.0.0.0)
	if err := router.Run("0.0.0.0:" + port); err != nil {
		log.Fatal("❌ Failed to start server:", err)
	}
}
