const express = require('express');
const cors = require('cors');
const path = require('path');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

// إعداد Express مع معالجة الأخطاء المتقدمة
const app = express();
const PORT = process.env.PORT || 8080;

console.log('🚀 Starting Stable Secure Server...');
console.log(`📡 Server will run on port: ${PORT}`);

// إعداد Prisma مع معالجة الأخطاء
let prisma;
try {
  prisma = new PrismaClient({
    log: ['error', 'warn'],
    errorFormat: 'pretty'
  });
} catch (error) {
  console.error('❌ Prisma initialization error:', error);
  process.exit(1);
}

// معالج الأخطاء العام
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  // لا نوقف الخادم، فقط نسجل الخطأ
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  // لا نوقف الخادم، فقط نسجل الخطأ
});

// إعدادات الحماية الأساسية
const helmetConfig = helmet({
  hidePoweredBy: true,
  frameguard: { action: 'deny' },
  noSniff: true,
  xssFilter: true,
  referrerPolicy: { policy: 'no-referrer' },
  contentSecurityPolicy: false // تعطيل CSP لتجنب مشاكل الواجهة الأمامية
});

// Rate Limiting مع إعدادات متوازنة
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: false,
    legacyHeaders: false,
    skip: (req) => {
      // تخطي Rate Limiting للطلبات المحلية في التطوير
      const isLocal = req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === '::ffff:127.0.0.1';
      return isLocal && process.env.NODE_ENV !== 'production';
    }
  });
};

const generalLimiter = createRateLimiter(15 * 60 * 1000, 200, 'تم تجاوز الحد المسموح من الطلبات لاتحاول');
const loginLimiter = createRateLimiter(15 * 60 * 1000, 10, 'تم تجاوز محاولات تسجيل الدخول المسموحة لاتحاول');
const apiLimiter = createRateLimiter(5 * 60 * 1000, 50, 'تم تجاوز الحد المسموح للعمليات الحساسة لاتحاول');

// Middleware للحماية
app.use(helmetConfig);
app.use(generalLimiter);

// إخفاء معلومات الخادم
app.use((req, res, next) => {
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');
  res.setHeader('Server', 'Apache/2.4.41');
  res.setHeader('X-Powered-By', 'PHP/7.4.3');
  next();
});

// CORS مع إعدادات مرنة
const corsOptions = {
  origin: function (origin, callback) {
    // السماح بجميع الأصول في التطوير
    if (!origin || process.env.NODE_ENV !== 'production') {
      return callback(null, true);
    }
    
    // قائمة الأصول المسموحة في الإنتاج
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:8080',
      'http://***********:8080',
      'http://**************:8080'
    ];
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('غير مسموح بواسطة CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Device-ID']
};

app.use(cors(corsOptions));

// Body parsing مع حدود آمنة
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    try {
      JSON.parse(buf);
    } catch (e) {
      res.status(400).json({ error: 'Invalid JSON format لاتحاول' });
      throw new Error('Invalid JSON');
    }
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging آمن مع تفاصيل مفيدة
app.use((req, res, next) => {
  const clientIP = req.headers['x-forwarded-for'] || 
                   req.connection.remoteAddress || 
                   req.socket.remoteAddress ||
                   req.ip;
  
  const timestamp = new Date().toISOString();
  const userAgent = req.get('User-Agent') || 'Unknown';
  
  console.log(`${timestamp} - ${req.method} ${req.path} from ${clientIP} - ${userAgent.substring(0, 50)}`);
  next();
});

// منع الوصول للملفات الحساسة
app.use((req, res, next) => {
  const blockedPaths = [
    '/.env', '/package.json', '/package-lock.json', '/node_modules',
    '/.git', '/prisma', '/logs', '/config', '/.vscode', '/src',
    '/server', '/.gitignore', '/README.md'
  ];
  
  const isBlocked = blockedPaths.some(blocked => 
    req.path.toLowerCase().includes(blocked.toLowerCase())
  );
  
  if (isBlocked) {
    console.log(`🚨 محاولة وصول لملف حساس: ${req.path} من IP: ${req.ip}`);
    return res.status(404).json({ error: 'الصفحة غير موجودة لاتحاول' });
  }
  next();
});

// منع SQL Injection مع patterns محسنة
app.use((req, res, next) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /(\'|\"|;|--|\*|\|)/,
    /(\bUNION\b.*\bSELECT\b)/i,
    /(\bINSERT\b.*\bINTO\b)/i,
    /(\bDROP\b.*\bTABLE\b)/i
  ];

  const checkForSQLInjection = (obj) => {
    if (!obj || typeof obj !== 'object') return false;
    
    for (let key in obj) {
      if (typeof obj[key] === 'string') {
        for (let pattern of sqlPatterns) {
          if (pattern.test(obj[key])) {
            console.log(`🚨 محاولة SQL Injection من IP: ${req.ip} - Pattern: ${obj[key]}`);
            return true;
          }
        }
      } else if (typeof obj[key] === 'object') {
        if (checkForSQLInjection(obj[key])) return true;
      }
    }
    return false;
  };

  if (checkForSQLInjection(req.query) || checkForSQLInjection(req.body)) {
    return res.status(400).json({ error: 'بيانات غير صحيحة لاتحاول' });
  }
  next();
});

// تتبع النشاط المشبوه
app.use((req, res, next) => {
  const suspiciousAgents = [
    /sqlmap/i, /nikto/i, /nmap/i, /masscan/i, /zap/i, /burp/i,
    /curl/i, /wget/i, /python-requests/i, /postman/i, /insomnia/i
  ];
  
  const userAgent = req.get('User-Agent') || '';
  
  if (suspiciousAgents.some(pattern => pattern.test(userAgent))) {
    console.log(`🚨 User Agent مشبوه: ${userAgent} من IP: ${req.ip}`);
    return res.status(403).json({ error: 'وصول مرفوض لاتحاول' });
  }
  next();
});

// JWT Secret مع fallback آمن
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key-change-in-production-' + Date.now();

// Middleware للتحقق من التوكن مع معالجة أخطاء محسنة
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    const deviceId = req.headers['x-device-id'];

    // المسارات العامة التي لا تحتاج توكن
    const publicPaths = [
      '/health', '/api/test', '/api/client/login', '/api/agent/login',
      '/api/external/health', '/api/external/stats'
    ];
    
    if (publicPaths.includes(req.path)) {
      return next();
    }

    if (!token) {
      return res.status(401).json({ error: 'Access token required لاتحاول' });
    }

    if (!deviceId) {
      return res.status(401).json({ error: 'Device ID required لاتحاول' });
    }

    // التحقق من التوكن
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // البحث عن المستخدم
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { 
        id: true, 
        username: true, 
        loginName: true, 
        deviceId: true, 
        isActive: true,
        permissions: true
      }
    });

    if (!user || !user.isActive) {
      return res.status(401).json({ error: 'User not found or inactive لاتحاول' });
    }

    // التحقق من Device ID (مرن)
    if (user.deviceId && user.deviceId !== deviceId) {
      // السماح بتحديث Device ID للمستخدم النشط
      await prisma.user.update({
        where: { id: user.id },
        data: { deviceId: deviceId }
      });
      console.log(`📱 Device ID updated for user: ${user.loginName}`);
    }

    req.user = user;
    req.deviceId = deviceId;
    next();

  } catch (error) {
    console.error('Authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(403).json({ error: 'Invalid token لاتحاول' });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(403).json({ error: 'Token expired لاتحاول' });
    } else {
      return res.status(500).json({ error: 'Authentication error لاتحاول' });
    }
  }
};

// اختبار الاتصال بقاعدة البيانات مع إعادة المحاولة
const testDatabaseConnection = async () => {
  let retries = 5;
  while (retries > 0) {
    try {
      await prisma.$connect();
      console.log('✅ Database connected successfully');
      
      // إحصائيات سريعة
      const [userCount, clientCount, agentCount] = await Promise.all([
        prisma.user.count().catch(() => 0),
        prisma.client.count().catch(() => 0),
        prisma.agent.count().catch(() => 0)
      ]);
      
      console.log(`📊 Database stats: Users: ${userCount}, Clients: ${clientCount}, Agents: ${agentCount}`);
      return true;
    } catch (error) {
      console.error(`❌ Database connection failed (${retries} retries left):`, error.message);
      retries--;
      if (retries > 0) {
        console.log('🔄 Retrying database connection in 2 seconds...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }
  console.error('❌ Failed to connect to database after all retries');
  return false;
};

// Health check محسن
app.get('/health', async (req, res) => {
  try {
    const startTime = Date.now();
    
    // فحص قاعدة البيانات
    await prisma.$queryRaw`SELECT 1`;
    const dbResponseTime = Date.now() - startTime;
    
    const [userCount, clientCount, agentCount] = await Promise.all([
      prisma.user.count().catch(() => 0),
      prisma.client.count().catch(() => 0),
      prisma.agent.count().catch(() => 0)
    ]);

    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      database: {
        status: 'connected',
        responseTime: `${dbResponseTime}ms`,
        users: userCount,
        clients: clientCount,
        agents: agentCount
      },
      server: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version
      }
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: 'Database connection failed'
    });
  }
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'API يعمل بشكل صحيح',
    timestamp: new Date().toISOString(),
    server: 'Stable Secure Server',
    version: '1.0.0',
    status: 'operational'
  });
});
