@echo off
chcp 65001 >nul
echo ========================================
echo    🔐 تشغيل الخادم المحمي الوحيد
echo ========================================
echo.

echo 📋 فحص النظام...

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js أولاً
    pause
    exit /b 1
)

echo ✅ Node.js متوفر

REM التحقق من وجود مجلد server
if not exist "server" (
    echo ❌ مجلد server غير موجود
    pause
    exit /b 1
)

echo ✅ مجلد server موجود

REM التحقق من وجود الخادم المحمي
if not exist "server\index.js" (
    echo ❌ الخادم المحمي غير موجود: server\index.js
    pause
    exit /b 1
)

echo ✅ الخادم المحمي موجود

REM التحقق من وجود ملف .env
if not exist "server\.env" (
    echo ⚠️  ملف .env غير موجود
    echo سيتم استخدام الإعدادات الافتراضية
)

echo ✅ إعدادات الخادم جاهزة

REM إيقاف أي خوادم سابقة
echo.
echo 🛑 إيقاف أي خوادم سابقة...
taskkill /F /IM node.exe /T >nul 2>&1

REM انتظار قصير
timeout /t 2 /nobreak >nul

echo.
echo 🚀 تشغيل الخادم المحمي الوحيد...
echo =====================================

cd server

echo 📡 الخادم سيعمل على:
echo ├─ محلي: http://localhost:8080
echo ├─ داخلي: http://**************:8080  
echo └─ خارجي: http://***********:8080

echo.
echo 🔐 الحماية المطبقة:
echo ├─ JWT Authentication ✅
echo ├─ Device ID Binding ✅
echo ├─ Rate Limiting ✅
echo ├─ Permission System ✅
echo ├─ Data Encryption ✅
echo ├─ SQL Injection Prevention ✅
echo └─ XSS Prevention ✅

echo.
echo 🚨 تحذير: جميع APIs محمية ولا يمكن الوصول إليها بدون توكن!
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo ========================================
echo.

REM تشغيل الخادم المحمي
node index.js

echo.
echo 🛑 تم إيقاف الخادم
pause
